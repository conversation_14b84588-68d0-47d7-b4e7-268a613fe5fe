import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Helper function to get authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().auth.student?.JWT_Token || {};
  return access;
};

// Get all packages
export const getPackages = createAsyncThunk(
  "packages/getAll", 
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${baseURL}api/packages/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch packages");
    }
  }
);

// Get single package by ID
export const getSinglePackage = createAsyncThunk(
  "packages/getSingle",
  async (packageId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${baseURL}api/packages/package_detail/${packageId}/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch package");
    }
  }
);

// Subscribe to a package
export const subscribeToPackage = createAsyncThunk(
  "packages/subscribe",
  async (packageId, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${baseURL}api/packages/${packageId}/subscribe`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to subscribe to package");
    }
  }
);

// See current subscription
export const seeSubscription = createAsyncThunk(
  "packages/seeSubscription",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(`${baseURL}api/packages/subscription`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch subscription");
    }
  }
);

// Package slice
const packageSlice = createSlice({
  name: "packages",
  initialState: {
    packages: [],
    subscription: null,
    loading: false,
    error: null,
    selectedPackageId: null,
  },
  reducers: {
    setSelectedPackageId: (state, action) => {
      state.selectedPackageId = action.payload;
    },
    clearSelectedPackage: (state) => {
      state.selectedPackageId = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all packages
      .addCase(getPackages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPackages.fulfilled, (state, action) => {
        state.loading = false;
        state.packages = action.payload;
        state.error = null;
      })
      .addCase(getPackages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Subscribe to a package  
      .addCase(subscribeToPackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(subscribeToPackage.fulfilled, (state, action) => {
        state.loading = false;
        state.subscription = action.payload;
      })
      .addCase(subscribeToPackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // See current subscription
      .addCase(seeSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(seeSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.subscription = action.payload;
      })
      .addCase(seeSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get single package
      .addCase(getSinglePackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSinglePackage.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getSinglePackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setSelectedPackageId, clearSelectedPackage } = packageSlice.actions;
export default packageSlice.reducer;
