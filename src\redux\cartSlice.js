import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Cart item structure:
// {
//   id: string,
//   name: string,
//   price: number,
//   quantity: number,
//   image?: string,
//   description?: string,
//   packageId?: string, // For package items
//   type: 'package' | 'product', // Item type
//   maxQuantity?: number
// }

// Load cart from AsyncStorage
export const loadCartFromStorage = createAsyncThunk(
  'cart/loadFromStorage',
  async (_, { rejectWithValue }) => {
    try {
      const cartData = await AsyncStorage.getItem('cart');
      return cartData ? JSON.parse(cartData) : { items: [], total: 0, itemCount: 0 };
    } catch (error) {
      return rejectWithValue('Failed to load cart from storage');
    }
  }
);

// Save cart to AsyncStorage
export const saveCartToStorage = createAsyncThunk(
  'cart/saveToStorage',
  async (cartData, { rejectWithValue }) => {
    try {
      await AsyncStorage.setItem('cart', JSON.stringify(cartData));
      return cartData;
    } catch (error) {
      return rejectWithValue('Failed to save cart to storage');
    }
  }
);

const calculateCartTotals = (items) => {
  const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
  return { total, itemCount };
};

const cartSlice = createSlice({
  name: 'cart',
  initialState: {
    items: [],
    total: 0,
    itemCount: 0,
    loading: false,
    error: null,
    lastAddedItem: null,
  },
  reducers: {
    addToCart: (state, action) => {
      const newItem = action.payload;
      const existingItemIndex = state.items.findIndex(item => 
        item.id === newItem.id && item.type === newItem.type
      );

      if (existingItemIndex >= 0) {
        // Item exists, update quantity
        const existingItem = state.items[existingItemIndex];
        const newQuantity = existingItem.quantity + (newItem.quantity || 1);
        
        // Check max quantity if specified
        if (newItem.maxQuantity && newQuantity > newItem.maxQuantity) {
          state.error = `Maximum quantity of ${newItem.maxQuantity} reached for ${newItem.name}`;
          return;
        }
        
        state.items[existingItemIndex].quantity = newQuantity;
      } else {
        // New item, add to cart
        state.items.push({
          ...newItem,
          quantity: newItem.quantity || 1,
        });
      }

      // Recalculate totals
      const totals = calculateCartTotals(state.items);
      state.total = totals.total;
      state.itemCount = totals.itemCount;
      state.lastAddedItem = newItem;
      state.error = null;
    },

    removeFromCart: (state, action) => {
      const { id, type } = action.payload;
      state.items = state.items.filter(item => 
        !(item.id === id && item.type === type)
      );
      
      // Recalculate totals
      const totals = calculateCartTotals(state.items);
      state.total = totals.total;
      state.itemCount = totals.itemCount;
    },

    updateQuantity: (state, action) => {
      const { id, type, quantity } = action.payload;
      const itemIndex = state.items.findIndex(item => 
        item.id === id && item.type === type
      );

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          // Remove item if quantity is 0 or less
          state.items.splice(itemIndex, 1);
        } else {
          // Check max quantity if specified
          const item = state.items[itemIndex];
          if (item.maxQuantity && quantity > item.maxQuantity) {
            state.error = `Maximum quantity of ${item.maxQuantity} reached for ${item.name}`;
            return;
          }
          
          state.items[itemIndex].quantity = quantity;
        }
        
        // Recalculate totals
        const totals = calculateCartTotals(state.items);
        state.total = totals.total;
        state.itemCount = totals.itemCount;
        state.error = null;
      }
    },

    clearCart: (state) => {
      state.items = [];
      state.total = 0;
      state.itemCount = 0;
      state.lastAddedItem = null;
      state.error = null;
    },

    clearError: (state) => {
      state.error = null;
    },

    clearLastAddedItem: (state) => {
      state.lastAddedItem = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Load cart from storage
      .addCase(loadCartFromStorage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadCartFromStorage.fulfilled, (state, action) => {
        state.loading = false;
        const { items = [], total = 0, itemCount = 0 } = action.payload;
        state.items = items;
        state.total = total;
        state.itemCount = itemCount;
      })
      .addCase(loadCartFromStorage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Save cart to storage
      .addCase(saveCartToStorage.pending, (state) => {
        state.loading = true;
      })
      .addCase(saveCartToStorage.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(saveCartToStorage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  clearError,
  clearLastAddedItem,
} = cartSlice.actions;

export default cartSlice.reducer;
