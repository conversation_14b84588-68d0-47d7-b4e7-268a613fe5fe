import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';
import { logLogin, logSignUp, setUserId, setUserProperties } from '../utils/analytics';

// Get baseURL from Expo constants (from app.config.js extra)
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Set default configurations for Axios
axios.defaults.withCredentials = true;
axios.defaults.baseURL = baseURL;

// Dummy CSRF token for React Native
const getCSRFToken = () => 'dummy-csrf-token';

// Register
export const registerStudent = createAsyncThunk(
  'auth/registerStudent',
  async ({ studentData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `/api/students/register/`,
        studentData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Registration failed');
    }
  }
);

// Login
export const loginStudent = createAsyncThunk(
  'auth/loginStudent',
  async ({ loginData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `/api/students/login/`,
        loginData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Login failed');
    }
  }
);

// Google login
export const googleLoginStudent = createAsyncThunk(
  'auth/googleLoginStudent',
  async ({ googleLoginData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `/api/students/login_with_goole_student/`,
        googleLoginData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Google login failed');
    }
  }
);

// Verify OTP
export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ otpData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `/api/students/verify-otp/`,
        otpData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'OTP verification failed');
    }
  }
);

// Resend OTP
export const resendOTP = createAsyncThunk(
  'auth/resendOTP',
  async (email, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `/api/resend-otp/`,
        { email },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to resend OTP');
    }
  }
);

// Get Student Profile
export const getStudentProfile = createAsyncThunk(
  'auth/getStudentProfile',
  async ({ id }, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const accessToken = state.auth.JWT_Token?.access;
      if (!accessToken) {
        return rejectWithValue('No access token available');
      }

      const response = await axios.get(
        `/api/students/object/${id}/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('getStudentProfile error:', error.response || error);
      return rejectWithValue(error.response?.data || 'Failed to fetch profile');
    }
  }
);

// Edit Student Profile
export const editStudentProfile = createAsyncThunk(
  'auth/editStudentProfile',
  async ({ profileData, id }, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const accessToken = state.auth.JWT_Token?.access;
      if (!accessToken) {
        return rejectWithValue('No access token available');
      }

      const response = await axios.patch(
        `/api/students/object/${id}/`,
        profileData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('editStudentProfile error:', error.response || error);
      return rejectWithValue(error.response?.data || 'Failed to update profile');
    }
  }
);

const initialState = {
  user: null,
  profile: null,
  isLoading: false,
  error: null,
  isAuthenticated: false,
  otpVerified: false,
  JWT_Token: null,
  hasCompletedWalkthrough: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,  reducers: {    logout: (state) => {
      state.user = null;
      state.profile = null;
      state.JWT_Token = null;
      state.isAuthenticated = false;
      state.otpVerified = false;
      state.error = null;
      state.hasCompletedWalkthrough = false;  // Reset on logout
    },
    clearError: (state) => {
      state.error = null;
    },
    completeWalkthrough: (state) => {
      state.hasCompletedWalkthrough = true;
    },
  },
  extraReducers: (builder) => {
    builder
      // registerStudent
      .addCase(registerStudent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerStudent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(registerStudent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // loginStudent
      .addCase(loginStudent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })      .addCase(loginStudent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.student;
        state.JWT_Token = action.payload.JWT_Token;
        state.isAuthenticated = true;
        state.hasCompletedWalkthrough = false;  // Reset on new login

        // Log analytics events
        const user = action.payload.student;
        if (user) {
          logLogin('email');
          setUserId(user.user?.id?.toString() || user.id?.toString());
          setUserProperties({
            user_type: user.subscription_type || 'free',
            course: user.course || 'unknown',
            language_preferred: user.language_preferred || 'english',
            account_status: user.account_status || 'active',
          });
        }
      })
      .addCase(loginStudent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // googleLoginStudent
      .addCase(googleLoginStudent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })      .addCase(googleLoginStudent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.student;
        state.JWT_Token = action.payload.JWT_Token;
        state.isAuthenticated = true;
        state.hasCompletedWalkthrough = false;  // Reset on new Google login

        // Log analytics events for Google login
        const user = action.payload.student;
        if (user) {
          logLogin('google');
          setUserId(user.user?.id?.toString() || user.id?.toString());
          setUserProperties({
            user_type: user.subscription_type || 'free',
            course: user.course || 'unknown',
            language_preferred: user.language_preferred || 'english',
            account_status: user.account_status || 'active',
          });
        }
      })
      .addCase(googleLoginStudent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // verifyOTP
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state) => {
        state.isLoading = false;
        state.otpVerified = true;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // resendOTP
      .addCase(resendOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendOTP.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(resendOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // getStudentProfile
      .addCase(getStudentProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getStudentProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
      })
      .addCase(getStudentProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // editStudentProfile
      .addCase(editStudentProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(editStudentProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
      })
      .addCase(editStudentProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { logout, clearError, completeWalkthrough } = authSlice.actions;
export default authSlice.reducer;
