import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Helper to get token
const getAuthToken = (getState) => {
  const state = getState();
  return state?.auth?.JWT_Token?.access;
};

// Sync contacts
export const syncContacts = createAsyncThunk(
  'contacts/sync',
  async (contactData, { getState, rejectWithValue }) => {
    try {
      console.log('syncContacts request data:', contactData); // Log request data
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }
      const response = await axios.post(
        `${baseURL}api/contacts/sync/`,
        contactData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
      console.log('syncContacts response data:', response.data); // Log response data
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error syncing contacts');
    }
  }
);

// Get matched contacts
export const getMatchedContacts = createAsyncThunk(
  'contacts/getMatched',
  async (_, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }
      const response = await axios.get(
        `${baseURL}api/contacts/matched/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching matched contacts');
    }
  }
);

const contactSlice = createSlice({
  name: 'contacts',
  initialState: {
    contacts: [],
    matchedContacts: [],
    isLoading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Sync Contacts cases
      .addCase(syncContacts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(syncContacts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contacts = action.payload;
      })
      .addCase(syncContacts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Get Matched Contacts cases
      .addCase(getMatchedContacts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getMatchedContacts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.matchedContacts = action.payload;
      })
      .addCase(getMatchedContacts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = contactSlice.actions;
export default contactSlice.reducer;
