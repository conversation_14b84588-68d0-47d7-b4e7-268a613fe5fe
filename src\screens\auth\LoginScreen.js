import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import * as Icons from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { loginStudent } from '../../redux/authSlice';
import { clearSelectedPackage } from '../../redux/packageSlice';
import Toast from 'react-native-toast-message';
import {
  trackScreenView,
  trackFormStart,
  trackFormSubmit,
  trackFormFieldFocus,
  trackButtonPress,
  trackNavigation
} from '../../utils/screenAnalytics';

export default function LoginScreen({ navigation }) {
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state) => state.auth);
  const { selectedPackageId } = useSelector((state) => state.packages || {});
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [formStarted, setFormStarted] = useState(false);

  useEffect(() => {
    // Track screen view
    trackScreenView('Login', {
      has_selected_package: !!selectedPackageId,
      selected_package_id: selectedPackageId,
    });
  }, [selectedPackageId]);

  const validateForm = () => {
    if (!username.trim()) {
      trackFormSubmit('login', 'Login', false, 'Username is required');
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Username is required',
      });
      return false;
    }
    if (!password.trim() || password.length < 6) {
      trackFormSubmit('login', 'Login', false, 'Password must be at least 6 characters');
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Password must be at least 6 characters',
      });
      return false;
    }
    return true;
  };
  const handleSuccessfulLogin = () => {
    trackFormSubmit('login', 'Login', true);

    Toast.show({
      type: 'success',
      text1: 'Welcome back!',
      text2: 'Login successful',
      visibilityTime: 2000,
    });

    if (selectedPackageId) {
      // Navigate to package screen and clear the selection
      trackNavigation('Login', 'PackagesScreen', 'login_redirect', {
        selected_package_id: selectedPackageId,
      });

      navigation.reset({
        index: 1,
        routes: [
          { name: 'Main' },
          {
            name: 'PackagesScreen',
            params: { selectedPackageId }
          },
        ],
      });
      dispatch(clearSelectedPackage());
    } else {
      // Navigate to WalkAround screen
      trackNavigation('Login', 'WalkAround', 'login_success');

      navigation.reset({
        index: 0,
        routes: [{ name: 'WalkAround' }],
      });
    }
  };

  const handleLogin = async () => {
    if (!formStarted) {
      trackFormStart('login', 'Login');
      setFormStarted(true);
    }

    trackButtonPress('sign_in', 'Login');

    if (!validateForm()) return;

    try {
      await dispatch(
        loginStudent({ loginData: { username, password } })
      ).unwrap();
      handleSuccessfulLogin();
    } catch (err) {
      trackFormSubmit('login', 'Login', false, err.message || 'Invalid credentials');

      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: 'Please enter correct credentials',
        visibilityTime: 3000,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View>
          <Text style={styles.title}>Welcome Back!</Text>
          <Text style={styles.subtitle}>Sign in to continue</Text>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Username"
              value={username}
              onChangeText={setUsername}
              onFocus={() => trackFormFieldFocus('username', 'login', 'Login')}
              autoCapitalize="none"
            />

            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                onFocus={() => trackFormFieldFocus('password', 'login', 'Login')}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => {
                  setShowPassword(!showPassword);
                  trackButtonPress('toggle_password_visibility', 'Login', {
                    show_password: !showPassword,
                  });
                }}
              >
                <Icons.Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={24}
                  color="#666"
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.button, isLoading && styles.buttonDisabled]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.buttonText}>Sign In</Text>
              )}
            </TouchableOpacity>

            <View style={styles.footer}>
              <Text style={styles.footerText}>Don't have an account?</Text>
              <TouchableOpacity onPress={() => {
                trackNavigation('Login', 'Signup', 'tap');
                navigation.navigate('Signup');
              }}>
                <Text style={styles.signupText}>Sign up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
  },
  input: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    fontSize: 16,
  },
  passwordContainer: {
    position: 'relative',
    width: '100%',
    marginBottom: 20,
  },
  passwordInput: {
    width: '100%',
    height: 50,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingRight: 50, // Space for the eye icon
    fontSize: 16,
  },
  eyeIcon: {
    position: 'absolute',
    right: 15,
    top: 13,
    padding: 5,
  },  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    backgroundColor: '#28a745',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButton: {
    backgroundColor: '#28a745',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    color: '#666',
    marginRight: 5,
  },
  signupText: {
    color: '#28a745',
    fontWeight: '600',
  },
});
