import React, { useRef } from 'react';
import { View, StyleSheet, Image, Animated, Pressable } from 'react-native';

const ScratchCard = ({ width = 150, height = 150, onScratchComplete, children }) => {
  const scratchOpacity = useRef(new Animated.Value(1)).current;
  const isCompleted = useRef(false);
  const handleReveal = () => {
    if (isCompleted.current) return;
    
    isCompleted.current = true;
    Animated.timing(scratchOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onScratchComplete?.();
    });
  };
  return (
    <Pressable onPress={handleReveal}>
      <View style={[styles.container, { width, height }]}>
        <View style={styles.contentContainer}>
          {children}
        </View>
        <Animated.View 
          style={[
            StyleSheet.absoluteFill,
            styles.scratchLayer,
            { opacity: scratchOpacity }
          ]}
        >
          <View style={styles.tapInstructions}>
            <Image
              source={require('../assets/scard.jpg')}
              style={styles.overlayImage}
              resizeMode="cover"
            />
          </View>
        </Animated.View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 16,
  },
  contentContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
  },
  scratchLayer: {
    backgroundColor: '#e9ecef',
  },
  overlayImage: {
    width: '100%',
    height: '100%',
  },
  tapInstructions: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  }
});

export default ScratchCard;
