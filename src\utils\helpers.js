import { Alert } from 'react-native';

export const showError = (message) => {
  Alert.alert('Error', message);
};

export const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const validateImage = (uri) => {
  if (!uri) return false;
  const validExtensions = ['.jpg', '.jpeg', '.png'];
  return validExtensions.some(ext => uri.toLowerCase().endsWith(ext));
};

export const truncateText = (text, maxLength = 100) => {
  if (!text || text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};
