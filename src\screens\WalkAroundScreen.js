import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { completeWalkthrough } from '../redux/authSlice';

const walkAroundImages = [
    require('../../assets/one.png'),
    require('../../assets/two.png'),
    require('../../assets/three.png'),
    require('../../assets/four.png'),
];

export default function WalkAroundScreen({ navigation }) {
  const dispatch = useDispatch();
  const [currentIndex, setCurrentIndex] = useState(0);
  const completeAndNavigate = () => {
    dispatch(completeWalkthrough());
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' }],
    });
  };

  const handleNext = () => {
    if (currentIndex < walkAroundImages.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      completeAndNavigate();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleSkip = () => {
    completeAndNavigate();
  };

  return (
    <View style={styles.container}>
      <Image
        source={walkAroundImages[currentIndex]}
        style={styles.image}
        resizeMode="stretch"
      />
      <View style={styles.overlay} />
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, { opacity: currentIndex === 0 ? 0.5 : 1 }]}
          onPress={handlePrevious}
          disabled={currentIndex === 0}
        >
          <Text style={styles.buttonText}>Previous</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleSkip}>
          <Text style={styles.buttonText}>Skip</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleNext}>
          <Text style={styles.buttonText}>
            {currentIndex === walkAroundImages.length - 1 ? 'Finish' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.pagination}>
        {walkAroundImages.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.activeDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: 'rgba(40, 167, 69, 0.9)',
    borderRadius: 25,
    minWidth: 100,
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  pagination: {
    position: 'absolute',
    bottom: 120,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#28a745',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
});
