import React, { useEffect, useState, useContext } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Dimensions, Animated } from 'react-native';
import { Card, TextInput } from 'react-native-paper';
import { useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { getAllScratchCards, scratchScratchCard, getWalletData, raiseWithdrawalRequest } from '../redux/rewardSlice';
import ReferralData from '../components/ReferralData';
import ScratchCard from '../components/ScratchCard';
import LottieView from 'lottie-react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { ThemeContext } from '../context/ThemeContext';
import BottomTabBar from '../components/BottomTabBar';

const RewardScreen = ({ navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const [scratchCards, setScratchCards] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [showPrize, setShowPrize] = useState(false);
  const [isPrizeWon, setIsPrizeWon] = useState(false);
  const [prizeValue, setPrizeValue] = useState(0);
  const [withdrawalAmount, setWithdrawalAmount] = useState('');
  const [upiId, setUpiId] = useState('');
  const [walletData, setWalletData] = useState({
    totalCash: '...',
    withdrawals: '...',
    settled: '...',
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    // Fetch scratch cards
    dispatch(getAllScratchCards())
      .unwrap()
      .then((data) => setScratchCards(data))
      .catch((error) => console.error('Failed to fetch scratch cards:', error));

    // Fetch wallet data
    dispatch(getWalletData())
      .unwrap()
      .then((data) => {
        setWalletData({
          totalCash: data.total_wallet_balance,
          withdrawals: data.pending_withdrawals_amount,
          settled: data.settled_withdrawals_amount,
        });
      })
      .catch((error) => console.error('Failed to fetch wallet data:', error));
  }, [dispatch]);

  const handleCompleteScratch = (value, cardId) => {
    setSelectedCard(cardId);
    setShowPrize(true);
    setPrizeValue(value);
    setIsPrizeWon(value > 0);
  };

  const handleClaimPrize = () => {
    if (selectedCard) {
      dispatch(scratchScratchCard(selectedCard))
        .unwrap()
        .then(() => {
          // Update scratch card status
          setScratchCards((prevCards) =>
            prevCards.map((card) =>
              card.id === selectedCard ? { ...card, is_scratched: true } : card
            )
          );
          // Refetch wallet data to show updated balance
          dispatch(getWalletData())
            .unwrap()
            .then((data) => {
              setWalletData({
                totalCash: data.total_wallet_balance,
                withdrawals: data.pending_withdrawals_amount,
                settled: data.settled_withdrawals_amount,
              });
            })
            .catch((error) => console.error('Failed to fetch wallet data:', error));
        })
        .catch((error) => console.error('Failed to scratch card:', error));
    }
    setShowPrize(false);
  };

  const handleWithdraw = () => {
    const newErrors = {};

    if (!upiId) {
      newErrors.upiId = 'Please enter a valid UPI ID.';
    }

    if (!withdrawalAmount || isNaN(withdrawalAmount) || parseFloat(withdrawalAmount) <= 0) {
      newErrors.amount = 'Please enter a valid withdrawal amount.';
    } else if (parseFloat(withdrawalAmount) > parseFloat(walletData.totalCash)) {
      newErrors.amount = 'Withdrawal amount cannot exceed wallet balance.';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const payload = {
      amount: parseFloat(withdrawalAmount),
      upi_id: upiId,
    };

    dispatch(raiseWithdrawalRequest(payload))
      .unwrap()
      .then(() => {
        Alert.alert('Success', 'Withdrawal request raised successfully!');
        setErrors({});
        setWithdrawalAmount('');
        setUpiId('');
      })
      .catch((error) => {
        if (error?.error) {
          Alert.alert('Error', error.error);
          setErrors({ apiError: error.error });
        } else {
          Alert.alert('Error', 'Failed to raise withdrawal request. Please try again later.');
          setErrors({ apiError: 'Failed to raise withdrawal request. Please try again later.' });
        }
      });
  };

  // Add focus effect to refetch data
  useEffect(() => {
    const loadData = () => {
      // Fetch wallet data
      dispatch(getWalletData())
        .unwrap()
        .then((data) => {
          setWalletData({
            totalCash: data.total_wallet_balance,
            withdrawals: data.pending_withdrawals_amount,
            settled: data.settled_withdrawals_amount,
          });
        })
        .catch((error) => console.error('Failed to fetch wallet data:', error));
        
      // Fetch scratch cards
      dispatch(getAllScratchCards())
        .unwrap()
        .then((data) => setScratchCards(data))
        .catch((error) => console.error('Failed to fetch scratch cards:', error));
    };

    loadData(); // Load data on mount

    // If navigation is available, set up the focus listener
    if (navigation?.addListener) {
      const unsubscribe = navigation.addListener('focus', loadData);
      return unsubscribe;
    }
  }, [navigation, dispatch]);

  const renderWalletCard = () => (
    <Card style={[styles.walletCard, isDarkMode && styles.walletCardDark]}>
      <Card.Title title="Nayan" titleStyle={[styles.cardTitle, isDarkMode && styles.textDark]} />
      <Card.Content>
        <View style={styles.walletInfo}>
          <View style={styles.walletItem}>
            <Icon name="money" size={24} color={isDarkMode ? "#2ecc71" : "#198754"} />
            <Text style={[styles.walletText, isDarkMode && styles.textDark]}>Wallet</Text>
            <Text style={[styles.walletText, isDarkMode && styles.textDark]}>₹{walletData.totalCash}</Text>
          </View>
          <View style={styles.walletItem}>
            <Icon name="arrow-circle-down" size={24} color={isDarkMode ? "#3498db" : "#0d6efd"} />
            <Text style={[styles.walletText, isDarkMode && styles.textDark]}>Pending</Text>
            <Text style={[styles.walletText, isDarkMode && styles.textDark]}>₹{walletData.withdrawals}</Text>
          </View>
          <View style={styles.walletItem}>
            <Icon name="check" size={24} color={isDarkMode ? "#f1c40f" : "#ffc107"} />
            <Text style={[styles.walletText, isDarkMode && styles.textDark]}>Settled</Text>
            <Text style={[styles.walletText, isDarkMode && styles.textDark]}>₹{walletData.settled}</Text>
          </View>
        </View>

        <TextInput
          label="Enter Your UPI ID"
          value={upiId}
          onChangeText={setUpiId}
          error={!!errors.upiId}
          style={[styles.input, isDarkMode && styles.inputDark]}
          theme={{ colors: { text: isDarkMode ? '#fff' : '#000' } }}
        />
        {errors.upiId && <Text style={styles.errorText}>{errors.upiId}</Text>}

        <TextInput
          label="Enter Amount to Withdraw"
          value={withdrawalAmount}
          onChangeText={setWithdrawalAmount}
          keyboardType="numeric"
          error={!!errors.amount}
          style={[styles.input, isDarkMode && styles.inputDark]}
          theme={{ colors: { text: isDarkMode ? '#fff' : '#000' } }}
        />
        {errors.amount && <Text style={styles.errorText}>{errors.amount}</Text>}

        {errors.apiError && <Text style={styles.errorText}>{errors.apiError}</Text>}

        <TouchableOpacity style={styles.withdrawButton} onPress={handleWithdraw}>
          <Text style={styles.withdrawButtonText}>Withdraw</Text>
        </TouchableOpacity>
      </Card.Content>
    </Card>
  );

  return (
    <GestureHandlerRootView style={[styles.container, isDarkMode && styles.containerDark]}>
      <BottomTabBar>
        <ScrollView>
          <View style={styles.content}>
            <View style={styles.referralSection}>
              <ReferralData profile={false} />
            </View>

            <View style={styles.walletSection}>
              {renderWalletCard()}
            </View>

            <View style={styles.scratchCardsSection}>
              {scratchCards.map((card) => (
                <View key={card.id} style={styles.scratchCardContainer}>
                  <Card style={[styles.scratchCard, isDarkMode && styles.scratchCardDark]}>
                    {card.is_scratched ? (
                      <Card.Content style={[styles.scratchedCard, isDarkMode && styles.scratchedCardDark]}>
                        <Icon name="bitcoin" size={36} color="#ffc107" />
                        <Text style={[styles.prizeText, isDarkMode && styles.textDark]}>₹{card.amount}</Text>
                        <Text style={[styles.scratchText, isDarkMode && styles.textDark]}>You won</Text>
                      </Card.Content>
                    ) : (
                      <View style={styles.scratchCardContent}>
                        <ScratchCard
                          onScratchComplete={() => handleCompleteScratch(card.amount, card.id)}
                        >
                          <View style={[styles.scratchCardBackground, isDarkMode && styles.scratchCardBackgroundDark]}>
                            <Icon name="bitcoin" size={36} color="#ffc107" />
                            <Text style={[styles.prizeText, isDarkMode && styles.textDark]}>₹{card.amount}</Text>
                            <Text style={[styles.scratchText, isDarkMode && styles.textDark]}>You won!</Text>
                          </View>
                        </ScratchCard>
                      </View>
                    )}
                  </Card>
                </View>
              ))}
            </View>
          </View>

          {showPrize && (
            <View style={styles.overlay}>
              <View style={[styles.prizePopup, isDarkMode && styles.prizePopupDark]}>
                {isPrizeWon ? (
                  <>
                    <LottieView
                      source={require('../assets/confetti.json')}
                      autoPlay
                      loop={false}
                      style={styles.confetti}
                    />
                    <Icon name="bitcoin" size={80} color="#ffc107" />
                    <Text style={[styles.prizePopupText, isDarkMode && styles.textDark]}>You've won ₹{prizeValue}!</Text>
                    <TouchableOpacity
                      style={[styles.prizeButton, styles.claimButton]}
                      onPress={handleClaimPrize}
                    >
                      <Text style={styles.buttonText}>Claim</Text>
                    </TouchableOpacity>
                  </>
                ) : (
                  <>
                    <Icon name="frown-o" size={80} color={isDarkMode ? "#ff4444" : "#dc3545"} />
                    <Text style={[styles.prizePopupText, isDarkMode && styles.textDark]}>Better luck next time!</Text>
                    <TouchableOpacity
                      style={[styles.prizeButton, styles.tryAgainButton]}
                      onPress={handleClaimPrize}
                    >
                      <Text style={styles.buttonText}>Try Again</Text>
                    </TouchableOpacity>
                  </>
                )}
              </View>
            </View>
          )}
        </ScrollView>
      </BottomTabBar>
    </GestureHandlerRootView>
  );
};

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  content: {
    padding: 16,
  },
  referralSection: {
    marginBottom: 16,
  },
  walletSection: {
    marginBottom: 16,
  },
  walletCard: {
    backgroundColor: '#ffffff',
    elevation: 4,
  },
  walletCardDark: {
    backgroundColor: '#1e1e1e',
  },
  cardTitle: {
    textAlign: 'center',
  },
  walletInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  walletItem: {
    alignItems: 'center',
  },
  walletText: {
    color: '#000000',
  },
  textDark: {
    color: '#ffffff',
  },
  input: {
    marginBottom: 8,
    backgroundColor: '#ffffff',
  },
  inputDark: {
    backgroundColor: '#333333',
  },
  errorText: {
    color: '#dc3545',
    fontSize: 12,
    marginBottom: 8,
  },
  withdrawButton: {
    backgroundColor: '#198754',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
  },
  withdrawButtonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  scratchCardsSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  scratchCardContainer: {
    margin: 8,
  },
  scratchCard: {
    width: 150,
    height: 150,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#ffffff',
  },
  scratchCardDark: {
    backgroundColor: '#1e1e1e',
  },
  scratchedCard: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    backgroundColor: '#ffffff',
  },
  scratchedCardDark: {
    backgroundColor: '#1e1e1e',
  },
  scratchCardContent: {
    width: 150,
    height: 150,
    position: 'relative',
  },
  scratchCardBackground: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
  },
  scratchCardBackgroundDark: {
    backgroundColor: '#1e1e1e',
  },
  scratchText: {
    color: '#6c757d',
    fontWeight: 'bold',
  },
  prizeText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 8,
    color: '#000000',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  prizePopup: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  prizePopupDark: {
    backgroundColor: '#1e1e1e',
  },
  prizePopupText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 16,
    textAlign: 'center',
    color: '#000000',
  },
  prizeButton: {
    padding: 12,
    borderRadius: 4,
    minWidth: 120,
    alignItems: 'center',
  },
  claimButton: {
    backgroundColor: '#198754',
  },
  tryAgainButton: {
    backgroundColor: '#6c757d',
  },
  buttonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  confetti: {
    position: 'absolute',
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH,
    pointerEvents: 'none',
  },
});

export default RewardScreen;
