import { logEvent, logScreenView, logViewContent, logSearch } from './analytics';

/**
 * Comprehensive Screen Analytics Tracking
 * 
 * This module provides standardized analytics tracking for all screens
 * and common user interactions throughout the app.
 */

// Screen tracking functions
export const trackScreenView = (screenName, additionalParams = {}) => {
  logScreenView(screenName, `${screenName}Screen`);
  logEvent('screen_view', {
    screen_name: screenName,
    ...additionalParams,
    timestamp: new Date().toISOString(),
  });
};

// Navigation tracking
export const trackNavigation = (fromScreen, toScreen, navigationMethod = 'tap', additionalParams = {}) => {
  logEvent('navigation', {
    from_screen: fromScreen,
    to_screen: toScreen,
    navigation_method: navigationMethod,
    ...additionalParams,
    timestamp: new Date().toISOString(),
  });
};

// Button/Action tracking
export const trackButtonPress = (buttonName, screenName, additionalParams = {}) => {
  logEvent('button_pressed', {
    button_name: buttonName,
    screen: screenName,
    ...additionalParams,
    timestamp: new Date().toISOString(),
  });
};

// Form interactions
export const trackFormStart = (formName, screenName) => {
  logEvent('form_start', {
    form_name: formName,
    screen: screenName,
    timestamp: new Date().toISOString(),
  });
};

export const trackFormSubmit = (formName, screenName, success = true, errorMessage = null) => {
  logEvent('form_submit', {
    form_name: formName,
    screen: screenName,
    success,
    error_message: errorMessage,
    timestamp: new Date().toISOString(),
  });
};

export const trackFormFieldFocus = (fieldName, formName, screenName) => {
  logEvent('form_field_focus', {
    field_name: fieldName,
    form_name: formName,
    screen: screenName,
    timestamp: new Date().toISOString(),
  });
};

// Search tracking
export const trackSearch = (searchTerm, screenName, resultsCount = null) => {
  logSearch(searchTerm);
  logEvent('search_performed', {
    search_term: searchTerm,
    screen: screenName,
    results_count: resultsCount,
    timestamp: new Date().toISOString(),
  });
};

// Content interaction tracking
export const trackContentView = (contentType, contentId, contentName, screenName, additionalParams = {}) => {
  logViewContent(contentType, contentId, contentName);
  logEvent('content_viewed', {
    content_type: contentType,
    content_id: contentId,
    content_name: contentName,
    screen: screenName,
    ...additionalParams,
    timestamp: new Date().toISOString(),
  });
};

export const trackContentShare = (contentType, contentId, contentName, shareMethod, screenName) => {
  logEvent('content_shared', {
    content_type: contentType,
    content_id: contentId,
    content_name: contentName,
    share_method: shareMethod,
    screen: screenName,
    timestamp: new Date().toISOString(),
  });
};

// Error tracking
export const trackError = (errorType, errorMessage, screenName, additionalParams = {}) => {
  logEvent('error_occurred', {
    error_type: errorType,
    error_message: errorMessage,
    screen: screenName,
    ...additionalParams,
    timestamp: new Date().toISOString(),
  });
};

// Performance tracking
export const trackLoadTime = (screenName, loadTimeMs, success = true) => {
  logEvent('screen_load_time', {
    screen: screenName,
    load_time_ms: loadTimeMs,
    success,
    timestamp: new Date().toISOString(),
  });
};

// User engagement tracking
export const trackTimeSpent = (screenName, timeSpentMs) => {
  logEvent('time_spent_on_screen', {
    screen: screenName,
    time_spent_ms: timeSpentMs,
    timestamp: new Date().toISOString(),
  });
};

export const trackScrollDepth = (screenName, scrollPercentage) => {
  logEvent('scroll_depth', {
    screen: screenName,
    scroll_percentage: scrollPercentage,
    timestamp: new Date().toISOString(),
  });
};

// Feature usage tracking
export const trackFeatureUsage = (featureName, screenName, additionalParams = {}) => {
  logEvent('feature_used', {
    feature_name: featureName,
    screen: screenName,
    ...additionalParams,
    timestamp: new Date().toISOString(),
  });
};

// Modal/Popup tracking
export const trackModalOpen = (modalName, screenName, trigger = 'user_action') => {
  logEvent('modal_opened', {
    modal_name: modalName,
    screen: screenName,
    trigger,
    timestamp: new Date().toISOString(),
  });
};

export const trackModalClose = (modalName, screenName, closeMethod = 'user_action') => {
  logEvent('modal_closed', {
    modal_name: modalName,
    screen: screenName,
    close_method: closeMethod,
    timestamp: new Date().toISOString(),
  });
};

// Tab/Section tracking
export const trackTabSwitch = (fromTab, toTab, screenName) => {
  logEvent('tab_switched', {
    from_tab: fromTab,
    to_tab: toTab,
    screen: screenName,
    timestamp: new Date().toISOString(),
  });
};

// Filter/Sort tracking
export const trackFilterApplied = (filterType, filterValue, screenName, resultsCount = null) => {
  logEvent('filter_applied', {
    filter_type: filterType,
    filter_value: filterValue,
    screen: screenName,
    results_count: resultsCount,
    timestamp: new Date().toISOString(),
  });
};

export const trackSortApplied = (sortType, sortOrder, screenName, resultsCount = null) => {
  logEvent('sort_applied', {
    sort_type: sortType,
    sort_order: sortOrder,
    screen: screenName,
    results_count: resultsCount,
    timestamp: new Date().toISOString(),
  });
};

// Download/Export tracking
export const trackDownload = (fileName, fileType, screenName, fileSize = null) => {
  logEvent('file_downloaded', {
    file_name: fileName,
    file_type: fileType,
    screen: screenName,
    file_size: fileSize,
    timestamp: new Date().toISOString(),
  });
};

// Notification tracking
export const trackNotificationReceived = (notificationType, notificationId) => {
  logEvent('notification_received', {
    notification_type: notificationType,
    notification_id: notificationId,
    timestamp: new Date().toISOString(),
  });
};

export const trackNotificationOpened = (notificationType, notificationId, screenName) => {
  logEvent('notification_opened', {
    notification_type: notificationType,
    notification_id: notificationId,
    screen: screenName,
    timestamp: new Date().toISOString(),
  });
};

// Session tracking
export const trackSessionStart = () => {
  logEvent('session_start', {
    timestamp: new Date().toISOString(),
  });
};

export const trackSessionEnd = (sessionDurationMs) => {
  logEvent('session_end', {
    session_duration_ms: sessionDurationMs,
    timestamp: new Date().toISOString(),
  });
};
