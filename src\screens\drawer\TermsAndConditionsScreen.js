import React, { useContext } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView } from 'react-native';
import BottomTabBar from '../../components/BottomTabBar';
import { ThemeContext } from '../../context/ThemeContext';

export default function TermsAndConditionsScreen() {
  const { isDarkMode } = useContext(ThemeContext);

  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: isDarkMode ? '#181818' : '#f5f5f5',
    },
    title: {
      ...styles.title,
      color: isDarkMode ? '#fff' : '#28a745',
    },
    subtitle: {
      ...styles.subtitle,
      color: isDarkMode ? '#eee' : '#666',
    },
    card: {
      ...styles.card,
      backgroundColor: isDarkMode ? '#232323' : '#fff',
      shadowColor: isDarkMode ? '#000' : '#000',
    },
    paragraph: {
      ...styles.paragraph,
      color: isDarkMode ? '#eee' : '#333',
    },
    sectionTitle: {
      ...styles.sectionTitle,
      color: isDarkMode ? '#fff' : '#222',
    },
    bold: {
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#333',
    },
    // For custom text in section content
    customText: {
      color: isDarkMode ? '#eee' : '#333',
      fontSize: 16,
      lineHeight: 24,
    }
  };

  return (
    <BottomTabBar>
      <SafeAreaView style={themedStyles.container}>
        <ScrollView>
          <View style={styles.headerSection}>
            {/* Centered, split heading */}
            <Text style={[themedStyles.title, styles.headingCenter]}>TERMS</Text>
            <Text style={[themedStyles.title, styles.headingCenter, {marginTop: 0}]}>&</Text>
            <Text style={[themedStyles.title, styles.headingCenter, {marginTop: 0}]}>CONDITIONS</Text>
            <Text style={themedStyles.subtitle}>Please read these terms and conditions carefully before using our services.</Text>
            <Text style={themedStyles.subtitle}>Last Updated: 2025</Text>
          </View>
          <View style={themedStyles.card}>
            <View style={styles.cardContent}>
              {sections(isDarkMode).map((section, index) => (
                <View key={index} style={styles.section}>
                  <Text style={themedStyles.sectionTitle}>{section.title}</Text>
                  {section.content.map((item, idx) => (
                    typeof item === 'string' ? (
                      <Text key={idx} style={themedStyles.paragraph}>{item}</Text>
                    ) : (
                      <View key={idx} style={{marginBottom: 10}}>
                        {item}
                      </View>
                    )
                  ))}
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </BottomTabBar>
  );
}

// sections now as a function to pass isDarkMode for correct coloring in custom content
const sections = (isDarkMode) => [
  {
    title: '1. Introduction',
    content: [
      'Purpose: These terms and conditions (the "Terms") are intended to legally bind you when you access or use our services, whether directly or indirectly.',
      'Scope: These Terms apply to the use of the Librainian web app, Android app, and iOS app, along with any related software.',
      'Effective Date: These Terms are effective as of July 7, 2024.'
    ]
  },
  {
    title: '2. Acceptance of Terms',
    content: [
      'User Consent: By clicking "Accept" or using the service, you consent to abide by these Terms of Use.',
      'Eligibility: Users must be 13 years of age or older, up to 85 years of age, to use the services.',
      'Amendments: We may update these Terms without your consent. If changes are made, you will be notified. Continuing to use our services implies acceptance of the changes.'
    ]
  },
  {
    title: '3. Definitions',
    content: [
      'Service: Librainian provides a digital platform for private libraries to manage seat allocation, fee collection, analytics, and streamline operations. It also allows libraries to use ad spaces on their walls through our network.',
      'User: Refers to the individual using the Librainian platform, which may include library staff, subscribers, trial users, and ad investors.',
      'Content: Content includes user-generated content and application content.',
      <View style={{marginLeft: 10}}>
        <Text style={{fontWeight: 'bold', color: isDarkMode ? '#fff' : '#333'}}>User-Generated Content:</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>Any materials, including profiles and feedback submitted by users, for which they are solely responsible.</Text>
        <Text style={{fontWeight: 'bold', color: isDarkMode ? '#fff' : '#333'}}>Application Content:</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>Content owned by Librainian, including text, graphics, logos, and intellectual property protected under copyright and trademark laws.</Text>
      </View>
    ]
  },
  {
    title: '4. Account Registration & Responsibilities',
    content: [
      'Account Creation: To create an account, users must provide their name, phone number, address, and a secure password.',
      'Accurate Information: You are obligated to provide accurate and up-to-date information for proper communication.',
      'Security: You are responsible for maintaining the confidentiality of your password and account.',
      'Unauthorized Use: In case of unauthorized access, your account will be blocked to ensure security.'
    ]
  },
  {
    title: '5. Service Description & Scope',
    content: [
      <View style={{marginBottom: 5}}>
        <Text style={{fontWeight: 'bold', color: isDarkMode ? '#fff' : '#333'}}>Core Features:</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Seat Allocation: Simplifies seat management for library users.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Fee Collection: Tracks and manages user payments.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Analytics and Reporting: Provides usage insights to library administrators.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Sublibrarian Feature: Allows delegation of tasks to sub-administrators in advanced plans.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• AdBrain: Enables libraries to sell ad spaces on their walls, generating revenue.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• 24/7 Accessibility: Access the platform at any time.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Credit Point System: Flexible usage system based on credits.</Text>
      </View>,
      <View style={{marginBottom: 5}}>
        <Text style={{fontWeight: 'bold', color: isDarkMode ? '#fff' : '#333'}}>Service Limitations:</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Subscription-based access based on your chosen plan.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Ad placement approval is required, and we reserve the right to reject ads violating policies.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Some services rely on third-party integrations and may be subject to their disruptions.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Service availability depends on your internet connectivity.</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• We may modify or discontinue services without prior notice.</Text>
      </View>,
      <View>
        <Text style={{fontWeight: 'bold', color: isDarkMode ? '#fff' : '#333'}}>Support Services:</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>Customer support is available 24/7 via email, chat (7 am to 10 pm), and calls (10 am to 6 pm on weekdays).</Text>
      </View>
    ]
  },
  {
    title: '6. User Obligations',
    content: [
      'Legal Use: Comply with local, state, and national laws.',
      'Prohibited Behavior: No hacking, fraud, or abuse.',
      'User Responsibility: You are liable for the content you share.'
    ]
  },
  {
    title: '7. Subscription and Fees',
    content: [
      'Subscription Plans & Coupons: Various plans with credit points and discounts available via coupons.',
      'Billing: Processed through Razorpay. Check the app for cycles and details.',
      'Late Payments: May result in service suspension.',
      'Refund Policy: No refunds. A 10-day trial is offered for evaluation.'
    ]
  },
  {
    title: '8. Free Trials and Promotions',
    content: [
      'Eligibility: Available to all new users.',
      'Duration: 10 days. Service will auto-renew unless canceled.'
    ]
  },
  {
    title: '9. Cancellation and Termination',
    content: [
      'User-Initiated Cancellation: Subscription ends upon non-renewal.',
      'Consequences: Access will be restricted.',
      'Service-Initiated Termination: May occur due to policy violations.'
    ]
  },
  {
    title: '10. Modifications to the Service and Terms',
    content: [
      'Right to Modify: We may change services or terms.',
      'Notifications: Updates posted on the website.',
      'Consent: Continued use = acceptance of changes.'
    ]
  },
  {
    title: '11. Intellectual Property Rights',
    content: [
      'Ownership: All IP belongs to Librainian (Pinak Venture).',
      'User License: Non-exclusive, non-transferable right to use.',
      'Restrictions: No copying, modifying, or reverse engineering.'
    ]
  },
  {
    title: '12. User-Generated Content',
    content: [
      'Content Ownership: You retain rights to your own content.',
      'License to Use: Grants us the right to use content for display/promotion.',
      'Content Guidelines: No harmful, unethical, or abusive material allowed.',
      'Content Removal: We reserve the right to remove violations and restrict accounts.'
    ]
  },
  {
    title: '13. Privacy Policy Integration',
    content: [
      'View Policy: [Link to Privacy Policy]',
      'Data Collection: As per our Privacy Policy.',
      'Consent: Use of platform implies agreement with our data practices.'
    ]
  },
  {
    title: '14. Data Security and Management',
    content: [
      'Security Measures: HTTPS, injection prevention, session security.',
      'Backups: Daily backups to prevent data loss.'
    ]
  },
  {
    title: '15. Third-Party Services & Integration',
    content: [
      'Integrations: Google Analytics, Razorpay, Gmail.',
      'Disclaimer: Not liable for failures caused by third parties.'
    ]
  },
  {
    title: '16. Limitation of Liability',
    content: [
      'Direct Damages: Limited to the last paid subscription amount.',
      'Indirect Damages: Not liable for any special or consequential damages.',
      'Indemnification: Users agree to hold Librainian harmless from legal claims.'
    ]
  },
  {
    title: '17. Governing Law & Jurisdiction',
    content: [
      'Applicable Law: Governed by the laws of [Insert Country/State].',
      'Jurisdiction: All disputes will be handled in [Insert Jurisdiction].'
    ]
  },
  {
    title: '18. Dispute Resolution',
    content: [
      'Arbitration: All disputes resolved via arbitration, not in court.'
    ]
  },
  {
    title: '19. Amendment of Terms',
    content: [
      'Right to Amend: We can update Terms anytime.',
      'Notification: Changes posted on the website.'
    ]
  },
  {
    title: '20. Contact Information',
    content: [
      'Email: <EMAIL>',
      'Phone: 6207628282',
      <View style={{marginLeft: 10}}>
        <Text style={{fontWeight: 'bold', color: isDarkMode ? '#fff' : '#333'}}>Business Hours:</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Email: 24/7</Text>
        <Text style={{color: isDarkMode ? '#eee' : '#333'}}>• Calls: Weekdays, 10 AM – 6 PM</Text>
      </View>
    ]
  },
  {
    title: '21. Miscellaneous Provisions',
    content: [
      'Severability: Invalid provisions don’t affect the rest.',
      'Waiver: Not enforcing rights doesn’t waive them.',
      'Entire Agreement: These Terms constitute the full agreement.',
      'Assignment: User rights are non-transferable.'
    ]
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: 16,
    backgroundColor: '#fff',
  },
  headerSection: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#28a745',
    textTransform: 'uppercase',
    marginTop: 20,
  },
  headingCenter: {
    textAlign: 'center',
    marginTop: 0,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
    color: '#666',
  },
  card: {
    margin: 15,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardContent: {
    padding: 15,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
    color: '#333',
  },
  bold: {
    fontWeight: 'bold',
  },
});
