import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Clipboard,
  Alert,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';
import notificationService from '../services/notificationService';

/**
 * PushTokenDisplay Component
 * 
 * Temporary component for testing push notifications
 * Shows the Expo Push Token with copy functionality
 * 
 * TODO: Remove this component after successful testing
 */
const PushTokenDisplay = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const [tokenStatus, setTokenStatus] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Get token status
    const status = notificationService.getTokenStatus();
    setTokenStatus(status);

    // Refresh token status every 2 seconds until we have a token
    const interval = setInterval(() => {
      const newStatus = notificationService.getTokenStatus();
      setTokenStatus(newStatus);
      
      // Stop checking once we have a token
      if (newStatus.hasToken) {
        clearInterval(interval);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const copyToken = () => {
    if (tokenStatus?.token) {
      Clipboard.setString(tokenStatus.token);
      Alert.alert(
        'Token Copied!',
        'Expo Push Token has been copied to clipboard.',
        [{ text: 'OK' }]
      );
    }
  };

  const copyCurlCommand = () => {
    if (tokenStatus?.curlCommand) {
      Clipboard.setString(tokenStatus.curlCommand);
      Alert.alert(
        'cURL Command Copied!',
        'Ready-to-use cURL command has been copied to clipboard. Paste it in terminal to test!',
        [{ text: 'OK' }]
      );
    }
  };

  if (!tokenStatus) {
    return (
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        <Text style={[styles.title, isDarkMode && styles.textDark]}>
          🔔 Loading Push Token...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, isDarkMode && styles.containerDark]}>
      {/* Header */}
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <Text style={[styles.title, isDarkMode && styles.textDark]}>
          🔔 Push Notifications Test
        </Text>
        <Icon 
          name={isExpanded ? 'chevron-up' : 'chevron-down'} 
          size={16} 
          color={isDarkMode ? '#fff' : '#333'} 
        />
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          {/* Device Status */}
          <View style={styles.statusRow}>
            <Text style={[styles.label, isDarkMode && styles.textDark]}>
              Device Type:
            </Text>
            <Text style={[
              styles.status, 
              isDarkMode && styles.textDark,
              tokenStatus.isPhysicalDevice ? styles.statusSuccess : styles.statusWarning
            ]}>
              {tokenStatus.isPhysicalDevice ? '📱 Physical Device' : '🖥️ Simulator/Emulator'}
            </Text>
          </View>

          {/* Token Status */}
          <View style={styles.statusRow}>
            <Text style={[styles.label, isDarkMode && styles.textDark]}>
              Token Status:
            </Text>
            <Text style={[
              styles.status, 
              isDarkMode && styles.textDark,
              tokenStatus.hasToken ? styles.statusSuccess : styles.statusError
            ]}>
              {tokenStatus.hasToken ? '✅ Generated' : '❌ Not Available'}
            </Text>
          </View>

          {/* Warning for Expo Go */}
          {!tokenStatus.isPhysicalDevice && (
            <View style={[styles.warningBox, isDarkMode && styles.warningBoxDark]}>
              <Text style={[styles.warningText, isDarkMode && styles.warningTextDark]}>
                ⚠️ You're using Expo Go or simulator. Push notifications won't work properly. 
                Use a development build for full testing.
              </Text>
            </View>
          )}

          {/* Token Display */}
          {tokenStatus.hasToken && (
            <View>
              <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
                📱 Your Expo Push Token:
              </Text>
              
              <ScrollView 
                horizontal 
                style={[styles.tokenContainer, isDarkMode && styles.tokenContainerDark]}
                showsHorizontalScrollIndicator={false}
              >
                <Text style={[styles.tokenText, isDarkMode && styles.tokenTextDark]}>
                  {tokenStatus.token}
                </Text>
              </ScrollView>

              <TouchableOpacity style={styles.copyButton} onPress={copyToken}>
                <Icon name="copy" size={16} color="#fff" />
                <Text style={styles.copyButtonText}>Copy Token</Text>
              </TouchableOpacity>

              {/* cURL Command */}
              <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
                🧪 Test Command:
              </Text>
              
              <TouchableOpacity 
                style={[styles.curlButton, isDarkMode && styles.curlButtonDark]} 
                onPress={copyCurlCommand}
              >
                <Icon name="terminal" size={16} color={isDarkMode ? '#fff' : '#333'} />
                <Text style={[styles.curlButtonText, isDarkMode && styles.curlButtonTextDark]}>
                  Copy cURL Command
                </Text>
              </TouchableOpacity>

              {/* Instructions */}
              <View style={[styles.instructionsBox, isDarkMode && styles.instructionsBoxDark]}>
                <Text style={[styles.instructionsTitle, isDarkMode && styles.textDark]}>
                  📋 How to Test:
                </Text>
                <Text style={[styles.instructionsText, isDarkMode && styles.textDark]}>
                  1. Copy the cURL command above{'\n'}
                  2. Open terminal/command prompt{'\n'}
                  3. Paste and run the command{'\n'}
                  4. Check your device for notification!
                </Text>
              </View>
            </View>
          )}

          {/* No Token Message */}
          {!tokenStatus.hasToken && (
            <View style={[styles.noTokenBox, isDarkMode && styles.noTokenBoxDark]}>
              <Text style={[styles.noTokenText, isDarkMode && styles.textDark]}>
                ❌ No push token available. Make sure you're on a physical device or development build.
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  containerDark: {
    backgroundColor: '#1e1e1e',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  textDark: {
    color: '#fff',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    color: '#666',
  },
  status: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusSuccess: {
    color: '#4CAF50',
  },
  statusWarning: {
    color: '#FF9800',
  },
  statusError: {
    color: '#f44336',
  },
  warningBox: {
    backgroundColor: '#FFF3CD',
    padding: 12,
    borderRadius: 8,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: '#FFEAA7',
  },
  warningBoxDark: {
    backgroundColor: '#3E2723',
    borderColor: '#5D4037',
  },
  warningText: {
    fontSize: 12,
    color: '#856404',
    lineHeight: 16,
  },
  warningTextDark: {
    color: '#FFCC80',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  tokenContainer: {
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    maxHeight: 60,
  },
  tokenContainerDark: {
    backgroundColor: '#2a2a2a',
  },
  tokenText: {
    fontSize: 11,
    color: '#333',
    fontFamily: 'monospace',
  },
  tokenTextDark: {
    color: '#fff',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
    gap: 8,
  },
  copyButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  curlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  curlButtonDark: {
    backgroundColor: '#2a2a2a',
    borderColor: '#444',
  },
  curlButtonText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
  },
  curlButtonTextDark: {
    color: '#fff',
  },
  instructionsBox: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  instructionsBoxDark: {
    backgroundColor: '#1A237E',
  },
  instructionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  noTokenBox: {
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  noTokenBoxDark: {
    backgroundColor: '#3E2723',
  },
  noTokenText: {
    fontSize: 12,
    color: '#C62828',
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default PushTokenDisplay;
