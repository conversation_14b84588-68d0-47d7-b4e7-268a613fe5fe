import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';
import { logEvent, logPurchase } from '../utils/analytics';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;
console.log('🌐 Base URL:', baseURL);

// Helper to get auth token
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state.auth.JWT_Token?.access,
  };
};

// Get Razorpay Config
export const getRazorpayConfig = createAsyncThunk(
  'subscription/getRazorpayConfig',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🌐 Razorpay Config API URL:', `${baseURL}api/packages/razorpay-config/`);
      const configResponse = await axios.get(`${baseURL}api/packages/razorpay-config/`);
      console.log('✅ Razorpay Config Response:', configResponse.data);
      return configResponse.data;
    } catch (error) {
      console.error('❌ Razorpay Config API Error:', error);
      console.error('❌ Error Response:', error.response);
      console.error('❌ Error Data:', error.response?.data);
      console.error('❌ Error Status:', error.response?.status);

      // Check if it's an HTML error response
      if (typeof error.response?.data === 'string' && error.response.data.includes('<!DOCTYPE html>')) {
        console.error('❌ Server returned HTML error page instead of JSON');
        return rejectWithValue('Server error: The server encountered an internal error');
      }

      return rejectWithValue(error.response?.data || 'Failed to fetch Razorpay config');
    }
  }
);

// Create Subscription
export const createSubscription = createAsyncThunk(
  'subscription/createSubscription',
  async (data, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      console.log('🔑 Access Token:', accessToken ? 'Present' : 'Missing');
      console.log('📝 Subscription Data:', data);
      console.log('🌐 API URL:', `${baseURL}api/packages/subscriptions/`);

      const createResponse = await axios.post(`${baseURL}api/packages/subscriptions/`, data, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('✅ Subscription API Response:', createResponse.data);

      // Log analytics event for subscription creation
      logEvent('subscription_created', {
        package_id: data.package,
        student_id: data.student,
        subscription_id: createResponse.data.subscription_id,
        amount: createResponse.data.final_price,
        currency: createResponse.data.currency || 'INR',
      });

      return createResponse.data;
    } catch (error) {
      console.error('❌ Subscription API Error:', error);
      console.error('❌ Error Response:', error.response);
      console.error('❌ Error Data:', error.response?.data);
      console.error('❌ Error Status:', error.response?.status);

      // Check if it's an HTML error response
      if (typeof error.response?.data === 'string' && error.response.data.includes('<!DOCTYPE html>')) {
        console.error('❌ Server returned HTML error page instead of JSON');
        return rejectWithValue('Server error: The server encountered an internal error');
      }

      return rejectWithValue(error.response?.data || 'Failed to create subscription');
    }
  }
);

// Verify Payment
export const verifyPayment = createAsyncThunk(
  'subscription/verifyPayment',
  async (verificationData, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const verifyResponse = await axios.post(`${baseURL}api/packages/verify_payment/`, verificationData, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return verifyResponse.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to verify payment');
    }
  }
);

// Validate Coupon Code
export const validateCouponCode = createAsyncThunk(
  'subscription/validateCouponCode',
  async (code, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const couponResponse = await axios.post(`${baseURL}api/packages/validate-coupon/`, code, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return couponResponse.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Invalid coupon code');
    }
  }
);

// Validate Gift Card
export const validateGiftCard = createAsyncThunk(
  'subscription/validateGiftCard',
  async (code, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const giftCardResponse = await axios.post(`${baseURL}api/packages/giftcards/validate/`, code, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return giftCardResponse.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Invalid gift card code');
    }
  }
);

// Create Cart Order (for multiple items)
export const createCartOrder = createAsyncThunk(
  'subscription/createCartOrder',
  async (orderData, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const orderResponse = await axios.post(`${baseURL}api/orders/cart/`, orderData, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return orderResponse.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create cart order');
    }
  }
);

// Get Order History
export const getOrderHistory = createAsyncThunk(
  'subscription/getOrderHistory',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const historyResponse = await axios.get(`${baseURL}api/orders/history/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return historyResponse.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch order history');
    }
  }
);

// Get Order Details
export const getOrderDetails = createAsyncThunk(
  'subscription/getOrderDetails',
  async (orderId, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const orderResponse = await axios.get(`${baseURL}api/orders/${orderId}/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return orderResponse.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch order details');
    }
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState: {
    config: null,
    subscription: null,
    verifyStatus: null,
    couponValidation: null,
    giftCardValidation: null,
    cartOrder: null,
    orderHistory: [],
    currentOrder: null,
    loading: false,
    error: null,
  },
  reducers: {
    resetVerifyStatus: (state) => {
      state.verifyStatus = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Razorpay Config
      .addCase(getRazorpayConfig.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getRazorpayConfig.fulfilled, (state, action) => {
        state.loading = false;
        state.config = action.payload;
      })
      .addCase(getRazorpayConfig.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create Subscription
      .addCase(createSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.subscription = action.payload;
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Verify Payment
      .addCase(verifyPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.verifyStatus = null;
      })
      .addCase(verifyPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.verifyStatus = action.payload;
      })
      .addCase(verifyPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.verifyStatus = null;
      })

      // Validate Coupon Code
      .addCase(validateCouponCode.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.couponValidation = null;
      })
      .addCase(validateCouponCode.fulfilled, (state, action) => {
        state.loading = false;
        state.couponValidation = action.payload;
      })
      .addCase(validateCouponCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.couponValidation = null;
      })

      // Validate Gift Card
      .addCase(validateGiftCard.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.giftCardValidation = null;
      })
      .addCase(validateGiftCard.fulfilled, (state, action) => {
        state.loading = false;
        state.giftCardValidation = action.payload;
      })
      .addCase(validateGiftCard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.giftCardValidation = null;
      })

      // Create Cart Order
      .addCase(createCartOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCartOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.cartOrder = action.payload;
      })
      .addCase(createCartOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Order History
      .addCase(getOrderHistory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getOrderHistory.fulfilled, (state, action) => {
        state.loading = false;
        state.orderHistory = action.payload;
      })
      .addCase(getOrderHistory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Order Details
      .addCase(getOrderDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getOrderDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.currentOrder = action.payload;
      })
      .addCase(getOrderDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetVerifyStatus } = subscriptionSlice.actions;
export default subscriptionSlice.reducer;
