import React, { useEffect, useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { SkeletonCard } from '../components/SkeletonCard';
import { useDispatch, useSelector } from 'react-redux';
import { getTestSeries, toggleSaveTestSeries } from '../redux/dashboardSlice';
import { setHasSeenWelcomePopup } from '../redux/popupSlice';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { ThemeContext } from '../context/ThemeContext';
import Toast from 'react-native-toast-message';
import BannerComponent from '../components/BannerComponent';
import { SkeletonBanner } from '../components/SkeletonBanner';
import { PassPopup } from '../components/PassPopup';
import { logEvent, logScreenView, logSearch, logViewContent } from '../utils/analytics';
import HtmlRenderer from '../components/HtmlRenderer';

const windowWidth = Dimensions.get('window').width;

export default function HomeScreen() {
  const [showPopup, setShowPopup] = useState(false);
  const [filteredData, setFilteredData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6; // Reduced from 9 for mobile
  const { savedTestSeries = [] } = useSelector((state) => state.dashboard);
  const { hasSeenWelcomePopup } = useSelector((state) => state.popup);

  const { testSeries: data, isLoading: loading, error } = useSelector(state => state.dashboard);

  const dispatch = useDispatch();
  const navigation = useNavigation();
  const { isDarkMode } = useContext(ThemeContext);

  useEffect(() => {
    // Log screen view
    logScreenView('Home', 'HomeScreen');

    dispatch(getTestSeries());
    // Show popup if user hasn't seen it yet
    if (!hasSeenWelcomePopup) {
      setShowPopup(true);
      logEvent('welcome_popup_shown', {
        screen: 'Home',
        timestamp: new Date().toISOString(),
      });
    }
  }, [dispatch, hasSeenWelcomePopup]);

  useEffect(() => {
    if (data?.length > 0) {
      const filtered = data.filter((test) =>
        test?.course?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredData(filtered);
    }
  }, [searchQuery, data]);

  const handleSearch = (text) => {
    setSearchQuery(text);
    setCurrentPage(1);

    // Log search analytics
    if (text.length > 0) {
      logSearch(text);
      logEvent('home_search', {
        search_term: text,
        screen: 'Home',
        timestamp: new Date().toISOString(),
      });
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);

    // Log pagination analytics
    logEvent('home_pagination', {
      page_number: pageNumber,
      total_items: filteredData.length,
      search_query: searchQuery || '',
      screen: 'Home',
      timestamp: new Date().toISOString(),
    });
  };
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  const handleSaveTestSeries = (test) => {
    if (!test?.subcourse_slug) {
      logEvent('save_test_series_failed', {
        reason: 'missing_subcourse_slug',
        test_name: test?.sub_course_name || 'unknown',
        screen: 'Home',
      });

      Toast.show({
        type: 'error',
        text1: 'Unable to save',
        text2: 'This test series cannot be saved at the moment',
        visibilityTime: 2000,
        position: 'bottom',
      });
      return;
    }

    const isSaved = savedTestSeries?.some(saved => saved?.subcourse_slug === test.subcourse_slug);
    dispatch(toggleSaveTestSeries(test));

    // Log analytics for save/unsave action
    logEvent(isSaved ? 'test_series_unsaved' : 'test_series_saved', {
      test_series_id: test.subcourse_slug || 'unknown',
      test_series_name: test.sub_course_name || 'unknown',
      course: test.course || 'unknown',
      is_paid: Boolean(test.paid),
      language: test.language || 'unknown',
      screen: 'Home',
      timestamp: new Date().toISOString(),
    });

    Toast.show({
      type: isSaved ? 'info' : 'success',
      text1: isSaved ? 'Test Series Removed' : 'Test Series Saved',
      text2: isSaved ? 'Removed from your saved items' : 'Added to your saved items',
      visibilityTime: 2000,
      position: 'bottom',
    });
  };  const renderTestCard = (test) => {
    // Check if test series is saved using subcourse_slug
    const isSaved = test?.subcourse_slug ? savedTestSeries?.some(saved => saved?.subcourse_slug === test?.subcourse_slug) ?? false : false;
    return (
      <View
        style={[
          styles.card,
          isDarkMode && styles.cardDark,
          { elevation: 3, shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.25, shadowRadius: 3.84 }
        ]}
      >
        <View style={styles.cardHeader}>
          <View style={styles.badgeContainer}>
            <View style={styles.iconBadge}>
              <Icon name="notebook" size={18} color="#fff" style={{ marginRight: 5 }} />
              <Text style={styles.badgeText}>{test?.course}</Text>
            </View>
            <View style={[styles.badge, { backgroundColor: test?.paid ? '#dc3545' : '#198754' }]}>
              <Text style={styles.badgeText}>{test?.paid ? 'Paid' : 'Free'}</Text>
            </View>
          </View>
        </View>

        <View style={styles.cardContent}>
          <View style={styles.titleContainer}>
            <HtmlRenderer
              html={test?.sub_course_name || ''}
              fontSize={16}
              style={{
                fontWeight: 'bold',
                color: isDarkMode ? '#e0e0e0' : '#000000',
                marginBottom: 8,
              }}
            />
          </View>
          <View style={styles.testInfo}>
            <Text style={[styles.smallText, isDarkMode && styles.textDark]}>
              {test?.paper_details?.length > 0
                ? `${test?.paper_details.length} Tests`
                : '0 Tests'}
            </Text>
          </View>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.addButton]}
              onPress={() => handleSaveTestSeries(test)}
            >
              <Icon name={isSaved ? "bookmark" : "bookmark-outline"} size={16} color="#fff" />
              <Text style={styles.buttonText}>
                {isSaved ? 'Saved' : 'Save Test Series'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.viewButton]}
              onPress={() => {
                // Log view content analytics - only if required fields exist
                if (test?.subcourse_slug && test?.sub_course_name) {
                  logViewContent('test_series', test.subcourse_slug, test.sub_course_name);
                  logEvent('test_series_viewed', {
                    test_series_id: test.subcourse_slug || 'unknown',
                    test_series_name: test.sub_course_name || 'unknown',
                    course: test.course || 'unknown',
                    is_paid: Boolean(test.paid),
                    language: test.language || 'unknown',
                    screen: 'Home',
                    timestamp: new Date().toISOString(),
                  });
                }

                const parent = navigation.getParent();
                const route = parent.getCurrentRoute();
                const params = { testSeries: test };

                if (route.name === 'MainTabs') {
                  navigation.navigate('TestSeriesDetails', params);
                } else {
                  parent.navigate('MainTabs', {
                    screen: 'HomeTab',
                    params: {
                      screen: 'TestSeriesDetails',
                      params
                    }
                  });
                }
              }}
            >
              <Icon name="eye" size={16} color="#198754" />
              <Text style={[styles.buttonText, { color: '#198754' }]}>View</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={[styles.cardFooter, isDarkMode && styles.cardFooterDark]}>
          <View style={styles.languageContainer}>
            <Icon name="translate" size={16} color={isDarkMode ? '#999' : '#666'} />
            <Text style={[styles.languageText, isDarkMode && styles.textDark]}>
              {test?.language}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderPagination = () => {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    return (
      <View style={styles.pagination}>
        <TouchableOpacity
          style={[
            styles.pageButton,
            currentPage === 1 && styles.pageButtonDisabled,
            isDarkMode && styles.pageButtonDark
          ]}
          onPress={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <Text style={[styles.pageButtonText, isDarkMode && styles.textDark]}>
            Previous
          </Text>
        </TouchableOpacity>

        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <TouchableOpacity
            key={page}
            style={[
              styles.pageButton,
              currentPage === page && styles.pageButtonActive,
              isDarkMode && styles.pageButtonDark
            ]}
            onPress={() => handlePageChange(page)}
          >
            <Text style={[
              styles.pageButtonText,
              currentPage === page && styles.pageButtonTextActive,
              isDarkMode && styles.textDark
            ]}>
              {page}
            </Text>
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          style={[
            styles.pageButton,
            currentPage === totalPages && styles.pageButtonDisabled,
            isDarkMode && styles.pageButtonDark
          ]}
          onPress={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <Text style={[styles.pageButtonText, isDarkMode && styles.textDark]}>
            Next
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (error) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.darkContainer]}>
        <View style={styles.centered}>
          <View style={styles.errorContainer}>            <Icon name="alert" size={24} color={isDarkMode ? '#ff4444' : '#dc3545'} />
            <Text style={[styles.errorText, isDarkMode && styles.textDark]}>
              {typeof error === 'string'
                ? error
                : typeof error?.detail === 'string'
                  ? error.detail
                  : Array.isArray(error?.messages)
                    ? error.messages.join(', ')
                    : typeof error?.messages === 'string'
                      ? error.messages
                      : 'An error occurred while loading test series'}
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => dispatch(getTestSeries())}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }  if (loading) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.darkContainer]}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={[styles.scrollView, isDarkMode && styles.scrollViewDark]}>
          <SkeletonBanner isDarkMode={isDarkMode} />
          <View style={[styles.content, isDarkMode && styles.contentDark]}>
            {[...Array(3)].map((_, index) => (
              <SkeletonCard key={index} isDarkMode={isDarkMode} />
            ))}
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.darkContainer]}>      <PassPopup 
        visible={showPopup} 
        onClose={() => {
          setShowPopup(false);
          dispatch(setHasSeenWelcomePopup(true));
        }}
      />
      <ScrollView 
        showsVerticalScrollIndicator={false}
        style={[styles.scrollView, isDarkMode && styles.scrollViewDark]}>
        <BannerComponent />
        
        <View style={styles.header}>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', alignItems: 'center', marginBottom: 15 }}>
            <Text style={[styles.headerTitle, isDarkMode && styles.textDark]}>Test Series </Text>
            <Text style={styles.headerTitle}>&</Text>
            <Text style={[styles.headerTitle, { color: 'transparent' }]}> </Text>
            <Text style={styles.greenText}>Free</Text>
            <Text style={[styles.headerTitle, { color: 'transparent' }]}> </Text>
            <Text style={styles.headerTitle}>Quizzes</Text>
          </View>
          <View style={[styles.searchContainer, isDarkMode && styles.searchContainerDark]}>
            <Icon name="magnify" size={20} color={isDarkMode ? '#e0e0e0' : '#666'} />
            <TextInput
              style={[styles.searchInput, isDarkMode && styles.searchInputDark]}
              placeholder="Search Test Series..."
              placeholderTextColor={isDarkMode ? '#999' : '#666'}
              value={searchQuery}
              onChangeText={handleSearch}
            />
          </View>
        </View>
        <View style={[styles.content, isDarkMode && styles.contentDark]}>
          {paginatedData.length > 0 ? (
            paginatedData.map((test, index) => (
              <View key={test?.subcourse_slug || `test-${index}`}>{renderTestCard(test)}</View>
            ))
          ) : (
            <Text style={[styles.noData, isDarkMode && styles.textDark]}>
              No test series found.
            </Text>
          )}
        </View>
        {filteredData.length > itemsPerPage && renderPagination()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  darkContainer: {
    backgroundColor: '#1a1a1a',
  },
  scrollView: {
    backgroundColor: '#f5f5f5',
  },
  scrollViewDark: {
    backgroundColor: '#1a1a1a',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#000000',
  },
  greenText: {
    color: '#198754',
  },
  textDark: {
    color: '#e0e0e0',
  },  header: {
    padding: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#000000',
  },
  greenText: {
    color: '#198754',
  },  
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 15,
    marginBottom: 0,
    width: '100%',
    maxWidth: 300,
    height: 40,
  },
  searchContainerDark: {
    backgroundColor: '#2d2d2d',
    borderColor: '#333',
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    height: 40,
    marginLeft: 8,
    color: '#000000',
  },
  searchInputDark: {
    color: '#e0e0e0',
  },  content: {
    paddingHorizontal: 10,
    paddingTop: 5,
    paddingBottom: 50,
    backgroundColor: '#f5f5f5',
  },
  contentDark: {
    backgroundColor: '#1a1a1a',
  },card: {
    marginBottom: 15,
    borderRadius: 8,
    backgroundColor: '#fff',
    elevation: 2,
    margin: 8,
  },
  cardDark: {
    backgroundColor: '#2d2d2d',
  }, cardHeader: {
    padding: 10,
  },
  cardContent: {
    padding: 10,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconBadge: {
    backgroundColor: '#198754',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  }, title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#000000',
  },
  titleContainer: {
    marginBottom: 8,
  },
  testInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  smallText: {
    fontSize: 12,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  }, button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },  addButton: {
    flex: 3,
    backgroundColor: '#198754',
  },
  savedButton: {
    flex: 3,
    backgroundColor: '#6c757d',
  },
  viewButton: {
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#198754',
  },
  buttonText: {
    fontSize: 12,
    color: '#fff',
    marginLeft: 4,
    fontWeight: '500',
  },
  cardFooter: {
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  cardFooterDark: {
    backgroundColor: '#1e1e1e',
  },
  languageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  languageText: {
    fontSize: 12,
    color: '#666',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 10,
  },
  pageButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  pageButtonDark: {
    backgroundColor: '#2d2d2d',
    borderColor: '#404040',
  },
  pageButtonActive: {
    backgroundColor: '#198754',
    borderColor: '#198754',
  },
  pageButtonDisabled: {
    backgroundColor: '#e9ecef',
    borderColor: '#dee2e6',
  },
  pageButtonText: {
    color: '#198754',
    fontSize: 14,
  },
  pageButtonTextActive: {
    color: '#fff',
  }, noData: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    marginTop: 8,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#198754',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
});
