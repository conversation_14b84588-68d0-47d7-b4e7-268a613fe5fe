# =============================================================================
# ENVIRONMENT CONFIGURATION EXAMPLE
# =============================================================================
# Copy this file to .env and update the values as needed
# For local development, create .env.local with your specific overrides

# =============================================================================
# ENVIRONMENT TYPE
# =============================================================================
NODE_ENV=development
EXPO_DEBUG=true

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Development API URLs (default)
EXPO_PUBLIC_WEBSITE_DOMAIN=https://your-dev-domain.com/
EXPO_PUBLIC_BASE_URL=https://your-dev-domain.com/
EXPO_PUBLIC_BLOGS=api/blogs/public-blogs/

# Production API URLs (uncomment for production builds)
# EXPO_PUBLIC_WEBSITE_DOMAIN=https://your-production-domain.com
# EXPO_PUBLIC_BASE_URL=https://your-production-domain.com/
# EXPO_PUBLIC_BLOGS=api/blogs/public-blogs/

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id

# =============================================================================
# GOOGLE AUTH CONFIGURATION
# =============================================================================
EXPO_PUBLIC_ANDROID_CLIENT_ID=your_android_client_id.apps.googleusercontent.com
EXPO_PUBLIC_IOS_CLIENT_ID=your_ios_client_id.apps.googleusercontent.com
EXPO_PUBLIC_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
EXPO_PUBLIC_EXPO_CLIENT_ID=your_expo_client_id.apps.googleusercontent.com

# =============================================================================
# EAS BUILD CONFIGURATION
# =============================================================================
# These variables are used by EAS builds and can be overridden per build profile

# Development Build URLs
EAS_DEV_WEBSITE_DOMAIN=https://your-dev-domain.com/
EAS_DEV_BASE_URL=https://your-dev-domain.com/

# Production Build URLs  
EAS_PROD_WEBSITE_DOMAIN=https://your-production-domain.com
EAS_PROD_BASE_URL=https://your-production-domain.com/
