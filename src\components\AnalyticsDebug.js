import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { logEvent, testAnalytics, logScreenView } from '../utils/analytics';

/**
 * Analytics Debug Component
 * 
 * This component helps debug Firebase Analytics issues.
 * Remove this component in production builds.
 */
const AnalyticsDebug = ({ visible = false }) => {
  const [analyticsStatus, setAnalyticsStatus] = useState('unknown');
  const [eventCount, setEventCount] = useState(0);

  useEffect(() => {
    checkAnalyticsStatus();
  }, []);

  const checkAnalyticsStatus = async () => {
    try {
      const isWorking = await testAnalytics();
      setAnalyticsStatus(isWorking ? 'working' : 'failed');
    } catch (error) {
      console.error('Analytics status check failed:', error);
      setAnalyticsStatus('error');
    }
  };

  const testBasicEvent = async () => {
    try {
      await logEvent('debug_test_event', {
        test_number: eventCount + 1,
        timestamp: new Date().toISOString(),
      });
      setEventCount(prev => prev + 1);
      Alert.alert('Success', `Test event ${eventCount + 1} logged successfully`);
    } catch (error) {
      Alert.alert('Error', `Failed to log test event: ${error.message}`);
    }
  };

  const testScreenView = async () => {
    try {
      await logScreenView('DebugScreen', 'AnalyticsDebugScreen');
      Alert.alert('Success', 'Screen view event logged successfully');
    } catch (error) {
      Alert.alert('Error', `Failed to log screen view: ${error.message}`);
    }
  };

  const testComplexEvent = async () => {
    try {
      await logEvent('debug_complex_event', {
        user_action: 'test_complex',
        screen: 'Debug',
        timestamp: new Date().toISOString(),
        test_data: {
          nested: 'object',
          array: [1, 2, 3],
          boolean: true,
          number: 42,
        },
      });
      Alert.alert('Success', 'Complex event logged successfully');
    } catch (error) {
      Alert.alert('Error', `Failed to log complex event: ${error.message}`);
    }
  };

  if (!visible) {
    return null;
  }

  const getStatusColor = () => {
    switch (analyticsStatus) {
      case 'working': return '#4CAF50';
      case 'failed': return '#f44336';
      case 'error': return '#ff9800';
      default: return '#9e9e9e';
    }
  };

  const getStatusText = () => {
    switch (analyticsStatus) {
      case 'working': return '✅ Working';
      case 'failed': return '❌ Failed';
      case 'error': return '⚠️ Error';
      default: return '🔄 Checking...';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔧 Analytics Debug</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Status:</Text>
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </View>

      <Text style={styles.eventCount}>Events logged: {eventCount}</Text>

      <TouchableOpacity style={styles.button} onPress={checkAnalyticsStatus}>
        <Text style={styles.buttonText}>🔄 Check Status</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testBasicEvent}>
        <Text style={styles.buttonText}>📊 Test Basic Event</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testScreenView}>
        <Text style={styles.buttonText}>📱 Test Screen View</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testComplexEvent}>
        <Text style={styles.buttonText}>🔧 Test Complex Event</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 15,
    borderRadius: 10,
    minWidth: 200,
    zIndex: 9999,
  },
  title: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusLabel: {
    color: 'white',
    fontSize: 14,
    marginRight: 5,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  eventCount: {
    color: 'white',
    fontSize: 12,
    marginBottom: 15,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 10,
    borderRadius: 5,
    marginBottom: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '600',
  },
});

export default AnalyticsDebug;
