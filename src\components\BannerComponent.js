import { useEffect, useState, useCallback, useContext, useRef } from 'react';
import { View, Image, StyleSheet, Dimensions, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Carousel from 'react-native-reanimated-carousel';
import { fetchBanners } from '../redux/bannerSlice';
import { ThemeContext } from '../context/ThemeContext';

const { width } = Dimensions.get('window');

const BannerComponent = () => {
  const dispatch = useDispatch();
  const { banners, isLoading } = useSelector((state) => state.banner);
  const { isDarkMode } = useContext(ThemeContext);
  const [autoPlay, setAutoPlay] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef(null);

  useEffect(() => {
    dispatch(fetchBanners());
  }, [dispatch]);

  useEffect(() => {
    // Enable autoplay when banners are loaded
    if (banners.length > 0) {
      setAutoPlay(true);
    }
  }, [banners]);

  const renderItem = useCallback(({ item }) => {
    return (
      <View style={styles.itemContainer}>
        <Image
          source={{ uri: item.banner_image }}
          style={styles.image}
          resizeMode="cover"
        />
      </View>
    );
  }, []);

  const renderIndicators = () => {
    if (banners.length <= 1) return null;

    return (
      <View style={styles.indicatorContainer}>
        {banners.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.indicator,
              isDarkMode && styles.indicatorDark,
              index === currentIndex && styles.activeIndicator
            ]}
            onPress={() => {
              setCurrentIndex(index);
              carouselRef.current?.scrollTo({ index, animated: true });
            }}
          />
        ))}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0066cc" />
      </View>
    );
  }

  if (!banners.length) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Carousel
        ref={carouselRef}
        loop
        width={width}
        height={(width / 2.1) * 1.2} // ✅ increase by 10% to match image zoom
        autoPlay={autoPlay}
        data={banners}
        scrollAnimationDuration={1000}
        autoPlayInterval={3000}
        renderItem={renderItem}
        onSnapToItem={(index) => setCurrentIndex(index)}
      />
      {renderIndicators()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  itemContainer: {
    flex: 1,
    width: '100%',
    overflow: 'hidden', // ensures zoomed-in image doesn't overflow the container visually
    alignItems: 'center', // centers the image if made larger
    justifyContent: 'center',
  },
  image: {
    width: '110%',
    height: '100%',
    transform: [{ scale: 1.2 }],
  },
  loadingContainer: {
    width: '100%',
    height: width / 2.1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 5,
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#d0d0d0',
    opacity: 0.6,
  },
  indicatorDark: {
    backgroundColor: '#666666',
  },
  activeIndicator: {
    backgroundColor: '#4CAF50',
    opacity: 1,
    transform: [{ scale: 1.2 }],
  },
});

export default BannerComponent;
