import { createSlice } from '@reduxjs/toolkit';

const popupSlice = createSlice({
  name: 'popup',
  initialState: {
    hasSeenWelcomePopup: false,
  },
  reducers: {
    setHasSeenWelcomePopup: (state, action) => {
      state.hasSeenWelcomePopup = action.payload;
    },
    resetPopupState: (state) => {
      state.hasSeenWelcomePopup = false;
    },
  },
});

export const { setHasSeenWelcomePopup, resetPopupState } = popupSlice.actions;
export default popupSlice.reducer;
