import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { useDispatch } from 'react-redux';
import Toast from 'react-native-toast-message';
import { resendOTP, verifyOTP } from '../../redux/authSlice';

export default function ForgotPasswordScreen({ navigation }) {
  const dispatch = useDispatch();
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [resendAttempts, setResendAttempts] = useState(3);

  const startResendTimer = () => {
    setResendTimer(60);
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSendOTP = async () => {
    if (!email.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please enter your email',
      });
      return;
    }

    setLoading(true);
    try {
      await dispatch(resendOTP(email)).unwrap();
      setShowOtpForm(true);
      startResendTimer();
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'OTP sent successfully',
      });
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: err || 'Failed to send OTP',
      });
    }
    setLoading(false);
  };

  const handleResendOTP = async () => {
    if (resendAttempts > 0 && resendTimer === 0) {
      try {
        await dispatch(resendOTP(email)).unwrap();
        setResendAttempts((prev) => prev - 1);
        startResendTimer();
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'OTP resent successfully',
        });
      } catch (err) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: err || 'Failed to resend OTP',
        });
      }
    }
  };

  const handleResetPassword = async () => {
    if (!otp.trim() || !newPassword.trim() || !confirmPassword.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please fill in all fields',
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Passwords do not match',
      });
      return;
    }

    setLoading(true);
    try {
      await dispatch(
        verifyOTP({
          otpData: {
            otp: otp,
            email_user: email,
            new_password: newPassword,
          },
        })
      ).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Password reset successful',
      });
      navigation.navigate('Login');
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: err || 'Failed to reset password',
      });
    }
    setLoading(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Reset Password</Text>
        <Text style={styles.subtitle}>
          {showOtpForm
            ? 'Enter the OTP sent to your email'
            : 'Enter your email to receive a reset code'}
        </Text>

        <View style={styles.form}>
          {!showOtpForm ? (
            <>
              <TextInput
                style={styles.input}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleSendOTP}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#ffffff" />
                ) : (
                  <Text style={styles.buttonText}>Send Reset Code</Text>
                )}
              </TouchableOpacity>
            </>
          ) : (
            <>
              <TextInput
                style={styles.input}
                placeholder="Enter OTP"
                value={otp}
                onChangeText={setOtp}
                keyboardType="number-pad"
                maxLength={6}
              />

              <TextInput
                style={styles.input}
                placeholder="New Password"
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry
              />

              <TextInput
                style={styles.input}
                placeholder="Confirm New Password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
              />

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleResetPassword}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#ffffff" />
                ) : (
                  <Text style={styles.buttonText}>Reset Password</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.button,
                  styles.resendButton,
                  (resendTimer > 0 || resendAttempts === 0) && styles.disabledButton,
                ]}
                onPress={handleResendOTP}
                disabled={resendTimer > 0 || resendAttempts === 0}
              >
                <Text
                  style={[
                    styles.buttonText,
                    styles.resendButtonText,
                    (resendTimer > 0 || resendAttempts === 0) && styles.disabledButtonText,
                  ]}
                >
                  {resendAttempts === 0
                    ? 'Maximum attempts reached'
                    : resendTimer > 0
                    ? `Resend OTP (${resendTimer}s)`
                    : 'Resend OTP'}
                </Text>
              </TouchableOpacity>

              <Text style={styles.attemptsText}>
                Remaining attempts: {resendAttempts}
              </Text>
            </>
          )}

          <TouchableOpacity
            style={styles.backToLogin}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.backToLoginText}>Back to Login</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  input: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    fontSize: 16,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  primaryButton: {
    backgroundColor: '#28a745',
  },
  resendButton: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#28a745',
  },
  disabledButton: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ccc',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  resendButtonText: {
    color: '#28a745',
  },
  disabledButtonText: {
    color: '#999',
  },
  attemptsText: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 20,
  },
  backToLogin: {
    alignItems: 'center',
  },
  backToLoginText: {
    color: '#28a745',
    fontSize: 16,
    fontWeight: '600',
  },
});
