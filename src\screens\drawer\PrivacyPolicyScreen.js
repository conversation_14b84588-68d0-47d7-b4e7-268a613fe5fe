import React, { useContext } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView } from 'react-native';
import BottomTabBar from '../../components/BottomTabBar';
import { ThemeContext } from '../../context/ThemeContext';

export default function PrivacyPolicyScreen() {
  const { isDarkMode } = useContext(ThemeContext);

  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: isDarkMode ? '#181818' : '#f5f5f5',
    },
    title: {
      ...styles.title,
      color: isDarkMode ? '#fff' : '#28a745',
    },
    subtitle: {
      ...styles.subtitle,
      color: isDarkMode ? '#aaa' : '#666',
    },
    card: {
      ...styles.card,
      backgroundColor: isDarkMode ? '#232323' : '#fff',
      shadowColor: isDarkMode ? '#000' : '#000',
    },
    paragraph: {
      ...styles.paragraph,
      color: isDarkMode ? '#eee' : '#333',
    },
    sectionTitle: {
      ...styles.sectionTitle,
      color: isDarkMode ? '#fff' : '#222',
    },
  };

  return (
    <BottomTabBar>
      <SafeAreaView style={themedStyles.container}>
        <ScrollView>
          <View style={styles.headerSection}>
            <Text style={themedStyles.title}>Privacy Policy</Text>
            <Text style={themedStyles.subtitle}>Your privacy is important to us.</Text>
          </View>
          <View style={themedStyles.card}>
            <View style={styles.cardContent}>
              {sections.map((section, index) => (
                <View key={index} style={styles.section}>
                  <Text style={themedStyles.sectionTitle}>{section.title}</Text>
                  {section.content.map((item, idx) => (
                    <Text key={idx} style={themedStyles.paragraph}>• {item}</Text>
                  ))}
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </BottomTabBar>
  );
}

const sections = [
  {
    title: '1. Introduction',
    content: [
      'Overview of Privacy Commitment: At Librainian, we take user privacy seriously and prioritize the security of your personal data.',
      'Scope: This privacy policy applies to registered users of our platform.',
      'Personal Data: Refers to any information related to an identified or identifiable person.',
      'Service: Refers to the Librainian platform, including its web and mobile applications.',
      'Third-party Analytical Tools: Refer to external services such as Google Analytics that help us understand user behavior.'
    ]
  },
  {
    title: '2. Data Collection',
    content: [
      'Personal Information: Full name, email address, contact information, father’s name, address, age, gender, location, locality, city, state, image (optional), and course.',
      'Financial Information: Payment details processed through Razorpay and PhonePe, including payment mode.',
      'Behavioral Data: Clickstream data and browsing patterns within the app.',
      'Sensitive Data: Not collected.',
      'Device Data: Metadata such as device IDs, mobile carrier, time zone.',
      'Collection via: registration forms, tracking tools, surveys, and support tickets.',
      'Third-party sources: Social media login and APIs (to be implemented).'
    ]
  },
  {
    title: '3. Purpose of Data Collection',
    content: [
      'Account management, service personalization, performance analytics.',
      'Communication via transactional and marketing emails.',
      'Security via fraud detection and monitoring.',
      'Cloudflare is used for firewall and encryption.'
    ]
  },
  {
    title: '4. Data Sharing',
    content: [
      'Shared with Razorpay, Google Analytics, Cloudflare, Microsoft Clarity, BulkSMS, Gmail.',
      'All vendors are under contract for data protection.',
      'Data shared with government bodies upon legal request.',
      'May be disclosed in legal disputes.'
    ]
  },
  {
    title: '5. Data Security',
    content: [
      'Encryption: SSL/TLS for transmission.',
      'Access is limited to authorized personnel.',
      'Monitoring and audit trails in place.',
      'Data breach protocol includes user notifications and mitigations.'
    ]
  },
  {
    title: '6. Data Retention',
    content: [
      'Data is retained while the account is active.',
      'Can be retained longer if required by law.',
      'Users can request deletion via support, with some exceptions.'
    ]
  },
  {
    title: '7. User Rights',
    content: [
      'Users may view, correct, delete, or export their data.',
      'Can object to certain processing activities including marketing.'
    ]
  },
  {
    title: '8. Cookies and Tracking',
    content: [
      'Types: Session, Persistent, Third-Party cookies.',
      'Management via browser settings and consent popup.',
      'Tracking tools include pixels and web beacons.'
    ]
  },
  {
    title: '9. International Data Transfers',
    content: [
      'Data may be stored or processed in the EU, US, etc.',
      'Safeguards like Standard Contractual Clauses in place.'
    ]
  },
  {
    title: '10. Minors and Children’s Privacy',
    content: [
      'Minimum age: [X] years.',
      'Parental consent required for minors.',
      'Parents can access and manage their child’s data.'
    ]
  },
  {
    title: '11. Changes to Privacy Policy',
    content: [
      'Previous versions will be accessible.',
      'Major updates will be highlighted.',
      'Effective from the posted date.'
    ]
  },
  {
    title: '12. User Consent',
    content: [
      'Consent is obtained during signup.',
      'Users can withdraw consent (may affect functionality).'
    ]
  },
  {
    title: '13. Legal Basis for Processing',
    content: [
      'Based on: Consent, Legitimate Interest, Contractual Necessity, Legal Obligations.'
    ]
  },
  {
    title: '14. Third-Party Links and Integrations',
    content: [
      'This policy does not cover external websites.',
      'OAuth/API integrations may share user data.'
    ]
  },
  {
    title: '15. Contact Information',
    content: [
      'Email: [your email]',
      'Address: [your mailing address]',
      'DPO Contact: [DPO contact details] (if applicable)'
    ]
  },
  {
    title: '16. Applicable Laws and Regulations',
    content: [
      'Compliant with GDPR, CCPA, etc.',
      'Regional legal variations are respected.'
    ]
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: 5,
    backgroundColor: '#fff',
  },
  headerSection: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#28a745',
    textTransform: 'uppercase',
    marginTop: 20,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
    color: '#666',
  },
  card: {
    margin: 15,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardContent: {
    padding: 15,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
    color: '#333',
  },
  bold: {
    fontWeight: 'bold',
  },
});
