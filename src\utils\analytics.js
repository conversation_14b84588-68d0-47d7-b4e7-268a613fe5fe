import * as Analytics from 'expo-firebase-analytics';

/**
 * Firebase Analytics Helper
 * 
 * This module provides a centralized way to handle Firebase Analytics
 * events throughout the application.
 */

class AnalyticsService {
  constructor() {
    this.isInitialized = false;
    this.initPromise = null;
    this.eventQueue = [];
    this.init();
  }

  /**
   * Initialize Firebase Analytics
   */
  async init() {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._performInit();
    return this.initPromise;
  }

  async _performInit() {
    try {
      // Temporarily disable Firebase Analytics to fix IOException
      console.log('⚠️ Firebase Analytics temporarily disabled to fix IOException');
      this.isInitialized = false;
      return;

      // Check if Analytics is available
      if (typeof Analytics === 'undefined') {
        throw new Error('expo-firebase-analytics is not available');
      }

      // Add a small delay to ensure Firebase is ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Test a simple analytics call to verify it's working
      await Analytics.logEvent('analytics_initialized', {
        timestamp: new Date().toISOString(),
      });

      this.isInitialized = true;
      console.log('📊 Firebase Analytics initialized successfully');

      // Process any queued events
      await this._processEventQueue();

    } catch (error) {
      console.error('❌ Failed to initialize Firebase Analytics:', error);
      this.isInitialized = false;

      // Fallback: still allow the app to work without analytics
      console.warn('⚠️ Analytics will be disabled for this session');
    }
  }

  /**
   * Process queued events
   */
  async _processEventQueue() {
    if (this.eventQueue.length > 0) {
      console.log(`📊 Processing ${this.eventQueue.length} queued events`);

      for (const queuedEvent of this.eventQueue) {
        try {
          await Analytics.logEvent(queuedEvent.eventName, queuedEvent.parameters);
          console.log(`📊 Queued event logged: ${queuedEvent.eventName}`);
        } catch (error) {
          console.error(`❌ Failed to log queued event ${queuedEvent.eventName}:`, error);
        }
      }

      this.eventQueue = [];
    }
  }

  /**
   * Log a custom event
   * @param {string} eventName - The name of the event
   * @param {object} parameters - Event parameters (optional)
   */
  async logEvent(eventName, parameters = {}) {
    // Validate event name first
    if (!eventName || typeof eventName !== 'string') {
      console.warn('⚠️ Invalid event name:', eventName);
      return;
    }

    // Clean parameters
    const cleanParameters = this.cleanParameters(parameters);

    if (!this.isInitialized) {
      // Queue the event if analytics isn't initialized yet
      this.eventQueue.push({ eventName, parameters: cleanParameters });
      console.log(`📊 Event queued: ${eventName} (analytics not ready)`);

      // Try to initialize if not already in progress
      if (!this.initPromise) {
        this.init();
      }
      return;
    }

    try {
      await Analytics.logEvent(eventName, cleanParameters);
      console.log(`📊 Event logged: ${eventName}`, cleanParameters);
    } catch (error) {
      console.error(`❌ Failed to log event ${eventName}:`, error);

      // Log additional debug information
      console.error('Event name type:', typeof eventName);
      console.error('Parameters type:', typeof parameters);
      console.error('Cleaned parameters:', cleanParameters);
    }
  }

  /**
   * Clean parameters by removing undefined, null, and empty values
   * @param {object} parameters - Parameters to clean
   * @returns {object} - Cleaned parameters
   */
  cleanParameters(parameters) {
    if (!parameters || typeof parameters !== 'object' || Array.isArray(parameters)) {
      return {};
    }

    const cleaned = {};
    for (const [key, value] of Object.entries(parameters)) {
      if (value !== undefined && value !== null && value !== '') {
        try {
          // Handle different value types
          if (typeof value === 'string') {
            cleaned[key] = value;
          } else if (typeof value === 'number' && !isNaN(value)) {
            cleaned[key] = value;
          } else if (typeof value === 'boolean') {
            cleaned[key] = value;
          } else if (Array.isArray(value)) {
            // Convert arrays to comma-separated strings
            cleaned[key] = value.join(', ');
          } else if (typeof value === 'object') {
            // Convert objects to JSON strings
            cleaned[key] = JSON.stringify(value);
          } else {
            // Convert other types to strings
            cleaned[key] = String(value);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to process parameter ${key}:`, error);
          // Skip this parameter if it can't be processed
        }
      }
    }
    return cleaned;
  }

  /**
   * Set user ID for analytics
   * @param {string} userId - The user ID
   */
  async setUserId(userId) {
    if (!this.isInitialized) {
      console.warn('⚠️ Analytics not initialized, skipping setUserId');
      return;
    }

    try {
      await Analytics.setUserId(userId);
      console.log(`📊 User ID set: ${userId}`);
    } catch (error) {
      console.error('❌ Failed to set user ID:', error);
    }
  }

  /**
   * Set user properties for analytics
   * @param {object} properties - User properties object
   */
  async setUserProperties(properties) {
    if (!this.isInitialized) {
      console.warn('⚠️ Analytics not initialized, skipping setUserProperties');
      return;
    }

    try {
      await Analytics.setUserProperties(properties);
      console.log('📊 User properties set:', properties);
    } catch (error) {
      console.error('❌ Failed to set user properties:', error);
    }
  }

  /**
   * Log screen view event
   * @param {string} screenName - The name of the screen
   * @param {string} screenClass - The class of the screen (optional)
   */
  async logScreenView(screenName, screenClass = null) {
    const parameters = {
      screen_name: screenName,
    };

    if (screenClass) {
      parameters.screen_class = screenClass;
    }

    await this.logEvent('screen_view', parameters);
  }

  /**
   * Log login event
   * @param {string} method - Login method (e.g., 'email', 'google', 'facebook')
   */
  async logLogin(method) {
    await this.logEvent('login', { method });
  }

  /**
   * Log signup event
   * @param {string} method - Signup method (e.g., 'email', 'google', 'facebook')
   */
  async logSignUp(method) {
    await this.logEvent('sign_up', { method });
  }

  /**
   * Log purchase event
   * @param {object} purchaseData - Purchase information
   */
  async logPurchase(purchaseData) {
    const {
      transactionId,
      value,
      currency = 'INR',
      items = [],
      ...otherParams
    } = purchaseData;

    await this.logEvent('purchase', {
      transaction_id: transactionId,
      value,
      currency,
      items,
      ...otherParams,
    });
  }

  /**
   * Log search event
   * @param {string} searchTerm - The search term
   */
  async logSearch(searchTerm) {
    await this.logEvent('search', { search_term: searchTerm });
  }

  /**
   * Log content view event
   * @param {string} contentType - Type of content (e.g., 'package', 'course', 'blog')
   * @param {string} contentId - ID of the content
   * @param {string} contentName - Name of the content
   */
  async logViewContent(contentType, contentId, contentName) {
    await this.logEvent('view_item', {
      content_type: contentType,
      item_id: contentId,
      item_name: contentName,
    });
  }

  /**
   * Test analytics functionality
   */
  async testAnalytics() {
    console.log('🧪 Testing Firebase Analytics...');

    try {
      await this.logEvent('analytics_test', {
        test_type: 'basic_functionality',
        timestamp: new Date().toISOString(),
      });
      console.log('✅ Analytics test passed');
      return true;
    } catch (error) {
      console.error('❌ Analytics test failed:', error);
      return false;
    }
  }
}

// Create and export a singleton instance
const analyticsService = new AnalyticsService();

// Export individual functions for convenience with parameter validation
export const logEvent = (eventName, parameters = {}) => {
  if (!eventName) {
    console.warn('⚠️ Event name is required for logEvent');
    return;
  }
  return analyticsService.logEvent(eventName, parameters || {});
};

export const setUserId = (userId) => {
  if (!userId) {
    console.warn('⚠️ User ID is required for setUserId');
    return;
  }
  return analyticsService.setUserId(userId);
};

export const setUserProperties = (properties = {}) => {
  return analyticsService.setUserProperties(properties || {});
};

export const logScreenView = (screenName, screenClass = null) => {
  if (!screenName) {
    console.warn('⚠️ Screen name is required for logScreenView');
    return;
  }
  return analyticsService.logScreenView(screenName, screenClass);
};

export const logLogin = (method = 'unknown') => {
  return analyticsService.logLogin(method);
};

export const logSignUp = (method = 'unknown') => {
  return analyticsService.logSignUp(method);
};

export const logPurchase = (purchaseData = {}) => {
  return analyticsService.logPurchase(purchaseData || {});
};

export const logSearch = (searchTerm) => {
  if (!searchTerm) {
    console.warn('⚠️ Search term is required for logSearch');
    return;
  }
  return analyticsService.logSearch(searchTerm);
};

export const logViewContent = (contentType, contentId, contentName) => {
  if (!contentType || !contentId || !contentName) {
    console.warn('⚠️ All parameters are required for logViewContent');
    return;
  }
  return analyticsService.logViewContent(contentType, contentId, contentName);
};

export const testAnalytics = () => {
  return analyticsService.testAnalytics();
};

// Export the service instance as default
export default analyticsService;
