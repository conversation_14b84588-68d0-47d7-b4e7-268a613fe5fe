import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';
import { getOrderDetails } from '../redux/subscriptionSlice';
import { LoadingSpinner } from '../components/LoadingSpinner';

const OrderDetailsScreen = ({ route, navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const { orderId } = route.params;
  const dispatch = useDispatch();
  const { currentOrder, loading, error } = useSelector((state) => state.subscription);

  useEffect(() => {
    fetchOrderDetails();
  }, [orderId]);

  const fetchOrderDetails = async () => {
    try {
      await dispatch(getOrderDetails(orderId)).unwrap();
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch order details');
    }
  };

  const formatPrice = (price) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'paid':
        return '#198754';
      case 'pending':
        return '#ffc107';
      case 'failed':
      case 'cancelled':
        return '#dc3545';
      default:
        return isDarkMode ? '#999' : '#666';
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'paid':
        return 'check-circle';
      case 'pending':
        return 'clock-o';
      case 'failed':
      case 'cancelled':
        return 'times-circle';
      default:
        return 'info-circle';
    }
  };

  const handleShareOrder = async () => {
    if (!currentOrder) return;

    const orderText = `Order #${currentOrder.id || currentOrder.order_number}
Date: ${formatDate(currentOrder.created_at || currentOrder.date)}
Status: ${currentOrder.status}
Total: ${formatPrice(currentOrder.total_amount || currentOrder.amount)}
Payment ID: ${currentOrder.payment_id || 'N/A'}`;

    try {
      await Share.share({
        message: orderText,
        title: 'Order Details',
      });
    } catch (error) {
      console.error('Error sharing order:', error);
    }
  };

  const handleContactSupport = () => {
    navigation.navigate('RaiseQuery', {
      orderDetails: currentOrder,
    });
  };

  const handleReorder = () => {
    if (currentOrder?.items && currentOrder.items.length > 0) {
      // Navigate to packages or add items to cart
      navigation.navigate('PackagesScreen');
    }
  };

  if (loading) {
    return (
      <View style={[
        styles.loadingContainer,
        isDarkMode && styles.loadingContainerDark
      ]}>
        <LoadingSpinner />
      </View>
    );
  }

  if (!currentOrder) {
    return (
      <View style={[
        styles.errorContainer,
        isDarkMode && styles.errorContainerDark
      ]}>
        <Icon name="exclamation-triangle" size={60} color="#dc3545" />
        <Text style={[
          styles.errorText,
          isDarkMode && styles.errorTextDark
        ]}>
          Order not found
        </Text>
        <TouchableOpacity
          style={[
            styles.retryButton,
            isDarkMode && styles.retryButtonDark
          ]}
          onPress={fetchOrderDetails}
        >
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={[
      styles.container,
      isDarkMode && styles.containerDark
    ]}>
      {/* Order Header */}
      <View style={[
        styles.headerCard,
        isDarkMode && styles.headerCardDark
      ]}>
        <View style={styles.headerTop}>
          <View style={styles.orderInfo}>
            <Text style={[
              styles.orderId,
              isDarkMode && styles.textDark
            ]}>
              Order #{currentOrder.id || currentOrder.order_number}
            </Text>
            <Text style={[
              styles.orderDate,
              isDarkMode && styles.orderDateDark
            ]}>
              {formatDate(currentOrder.created_at || currentOrder.date)}
            </Text>
          </View>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(currentOrder.status) }
          ]}>
            <Icon
              name={getStatusIcon(currentOrder.status)}
              size={14}
              color="#fff"
              style={styles.statusIcon}
            />
            <Text style={styles.statusText}>
              {currentOrder.status?.toUpperCase() || 'UNKNOWN'}
            </Text>
          </View>
        </View>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              isDarkMode && styles.actionButtonDark
            ]}
            onPress={handleShareOrder}
          >
            <Icon name="share" size={16} color={isDarkMode ? '#4CAF50' : '#198754'} />
            <Text style={[
              styles.actionButtonText,
              isDarkMode && styles.actionButtonTextDark
            ]}>
              Share
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.actionButton,
              isDarkMode && styles.actionButtonDark
            ]}
            onPress={handleContactSupport}
          >
            <Icon name="support" size={16} color={isDarkMode ? '#4CAF50' : '#198754'} />
            <Text style={[
              styles.actionButtonText,
              isDarkMode && styles.actionButtonTextDark
            ]}>
              Support
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Payment Information */}
      <View style={[
        styles.card,
        isDarkMode && styles.cardDark
      ]}>
        <Text style={[
          styles.cardTitle,
          isDarkMode && styles.textDark
        ]}>
          Payment Information
        </Text>

        <View style={styles.infoRow}>
          <Text style={[
            styles.infoLabel,
            isDarkMode && styles.infoLabelDark
          ]}>
            Payment ID:
          </Text>
          <Text style={[
            styles.infoValue,
            isDarkMode && styles.textDark
          ]}>
            {currentOrder.payment_id || 'N/A'}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={[
            styles.infoLabel,
            isDarkMode && styles.infoLabelDark
          ]}>
            Payment Method:
          </Text>
          <Text style={[
            styles.infoValue,
            isDarkMode && styles.textDark
          ]}>
            {currentOrder.payment_method || 'N/A'}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={[
            styles.infoLabel,
            isDarkMode && styles.infoLabelDark
          ]}>
            Transaction ID:
          </Text>
          <Text style={[
            styles.infoValue,
            isDarkMode && styles.textDark
          ]}>
            {currentOrder.transaction_id || 'N/A'}
          </Text>
        </View>
      </View>

      {/* Order Items */}
      <View style={[
        styles.card,
        isDarkMode && styles.cardDark
      ]}>
        <Text style={[
          styles.cardTitle,
          isDarkMode && styles.textDark
        ]}>
          Order Items
        </Text>

        {currentOrder.items?.map((item, index) => (
          <View key={index} style={styles.itemRow}>
            <View style={styles.itemInfo}>
              <Text style={[
                styles.itemName,
                isDarkMode && styles.textDark
              ]}>
                {item.name || item.package_name}
              </Text>
              {item.description && (
                <Text style={[
                  styles.itemDescription,
                  isDarkMode && styles.itemDescriptionDark
                ]}>
                  {item.description}
                </Text>
              )}
              {item.quantity && (
                <Text style={[
                  styles.itemQuantity,
                  isDarkMode && styles.itemQuantityDark
                ]}>
                  Qty: {item.quantity}
                </Text>
              )}
            </View>
            <Text style={[
              styles.itemPrice,
              isDarkMode && styles.textDark
            ]}>
              {formatPrice(item.price || item.amount)}
            </Text>
          </View>
        ))}
      </View>

      {/* Order Summary */}
      <View style={[
        styles.card,
        isDarkMode && styles.cardDark
      ]}>
        <Text style={[
          styles.cardTitle,
          isDarkMode && styles.textDark
        ]}>
          Order Summary
        </Text>

        <View style={styles.summaryRow}>
          <Text style={[
            styles.summaryLabel,
            isDarkMode && styles.summaryLabelDark
          ]}>
            Subtotal:
          </Text>
          <Text style={[
            styles.summaryValue,
            isDarkMode && styles.textDark
          ]}>
            {formatPrice(currentOrder.subtotal || currentOrder.total_amount)}
          </Text>
        </View>

        {currentOrder.discount_amount > 0 && (
          <View style={styles.summaryRow}>
            <Text style={[
              styles.summaryLabel,
              styles.discountLabel
            ]}>
              Discount:
            </Text>
            <Text style={[
              styles.summaryValue,
              styles.discountValue
            ]}>
              -{formatPrice(currentOrder.discount_amount)}
            </Text>
          </View>
        )}

        {currentOrder.tax_amount > 0 && (
          <View style={styles.summaryRow}>
            <Text style={[
              styles.summaryLabel,
              isDarkMode && styles.summaryLabelDark
            ]}>
              Tax:
            </Text>
            <Text style={[
              styles.summaryValue,
              isDarkMode && styles.textDark
            ]}>
              {formatPrice(currentOrder.tax_amount)}
            </Text>
          </View>
        )}

        <View style={[
          styles.summaryRow,
          styles.totalRow
        ]}>
          <Text style={[
            styles.totalLabel,
            isDarkMode && styles.textDark
          ]}>
            Total:
          </Text>
          <Text style={[
            styles.totalValue,
            isDarkMode && styles.textDark
          ]}>
            {formatPrice(currentOrder.total_amount || currentOrder.amount)}
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {currentOrder.status?.toLowerCase() === 'completed' && (
          <TouchableOpacity
            style={[
              styles.primaryButton,
              isDarkMode && styles.primaryButtonDark
            ]}
            onPress={handleReorder}
          >
            <Icon name="refresh" size={16} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.primaryButtonText}>Reorder</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.secondaryButton,
            isDarkMode && styles.secondaryButtonDark
          ]}
          onPress={() => navigation.navigate('PackagesScreen')}
        >
          <Icon name="shopping-bag" size={16} color={isDarkMode ? '#4CAF50' : '#198754'} style={styles.buttonIcon} />
          <Text style={[
            styles.secondaryButtonText,
            isDarkMode && styles.secondaryButtonTextDark
          ]}>
            Continue Shopping
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingContainerDark: {
    backgroundColor: '#121212',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 40,
  },
  errorContainerDark: {
    backgroundColor: '#121212',
  },
  errorText: {
    fontSize: 18,
    color: '#333',
    marginTop: 20,
    marginBottom: 20,
    textAlign: 'center',
  },
  errorTextDark: {
    color: '#fff',
  },
  retryButton: {
    backgroundColor: '#198754',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonDark: {
    backgroundColor: '#4CAF50',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  headerCard: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCardDark: {
    backgroundColor: '#1e1e1e',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
  },
  orderDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  orderDateDark: {
    color: '#999',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusIcon: {
    marginRight: 6,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  actionButtonDark: {
    backgroundColor: '#333',
  },
  actionButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
    color: '#198754',
  },
  actionButtonTextDark: {
    color: '#4CAF50',
  },
  card: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardDark: {
    backgroundColor: '#1e1e1e',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
  },
  textDark: {
    color: '#fff',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  infoLabelDark: {
    color: '#999',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemInfo: {
    flex: 1,
    marginRight: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  itemDescriptionDark: {
    color: '#999',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  itemQuantityDark: {
    color: '#999',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#198754',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryLabelDark: {
    color: '#999',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  discountLabel: {
    color: '#198754',
  },
  discountValue: {
    color: '#198754',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#198754',
  },
  actionButtons: {
    margin: 16,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: '#198754',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 8,
  },
  primaryButtonDark: {
    backgroundColor: '#4CAF50',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#198754',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 8,
  },
  secondaryButtonDark: {
    borderColor: '#4CAF50',
  },
  secondaryButtonText: {
    color: '#198754',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonTextDark: {
    color: '#4CAF50',
  },
  buttonIcon: {
    marginRight: 8,
  },
});

export default OrderDetailsScreen;
