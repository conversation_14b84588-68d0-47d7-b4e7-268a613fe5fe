import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Error reporting thunk
export const reportError = createAsyncThunk(
  'error/reportError',
  async ({ error_data }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${baseURL}api/customrcare/log-error/`,
        { error_data },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to report error');
    }
  }
);

const reportErrorSlice = createSlice({
  name: 'errorReport',
  initialState: {
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(reportError.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(reportError.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(reportError.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default reportErrorSlice.reducer;
