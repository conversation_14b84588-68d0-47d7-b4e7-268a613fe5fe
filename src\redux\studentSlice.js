import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Helper function to get authToken from Redux state
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state?.auth?.JWT_Token?.access,
    refreshToken: state?.auth?.JWT_Token?.refresh,
  };
};

// Get Student's Referral Data
export const getReferralData = createAsyncThunk(
  'student/getReferralData',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      if (!accessToken) {
        return rejectWithValue('No access token available');
      }

      const response = await axios.get(
        `${baseURL}api/students/generate-qr/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch referral data');
    }
  }
);

// Student slice
const studentSlice = createSlice({
  name: 'student',
  initialState: {
    loading: false,
    error: null,
    referralData: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Referral Data
      .addCase(getReferralData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReferralData.fulfilled, (state, action) => {
        state.loading = false;
        state.referralData = action.payload;
      })
      .addCase(getReferralData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = studentSlice.actions;
export default studentSlice.reducer;
