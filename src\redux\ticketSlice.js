import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Helper to get token
const getAuthToken = (getState) => {
  const state = getState();
  return state?.auth?.JWT_Token?.access;
};

export const createTicket = createAsyncThunk(
  'ticket/createTicket',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }

      const formData = new FormData();
      
      // Append text fields
      formData.append('customer_id', data.customer_id);
      formData.append('student_id', data.student_id);
      formData.append('ticket_status', data.ticket_status);
      formData.append('priority', data.priority);
      formData.append('subject', data.subject);
      formData.append('description', data.description);
      formData.append('resolve_summary', data.resolve_summary);
      formData.append('tags', data.tags);

      // Append image if exists
      if (data.attachments) {
        const { uri } = data.attachments;
        const uriParts = uri.split('.');
        const fileType = uriParts[uriParts.length - 1];
        
        formData.append('attachments', {
          uri: uri,
          name: `photo.${fileType}`,
          type: `image/${fileType}`,
        });
      }

      const response = await axios.post(
        `${baseURL}api/customrcare/tickets/`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Something went wrong');
    }
  }
);

export const getCustomerCareList = createAsyncThunk(
  'ticket/getCustomerCareList',
  async (_, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }

      const response = await axios.get(
        `${baseURL}api/customrcare/register/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Something went wrong');
    }
  }
);

const ticketSlice = createSlice({
  name: 'ticket',
  initialState: {
    tickets: [],
    status: 'idle',
    error: null,
    customerCareList: [],
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createTicket.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createTicket.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.tickets.push(action.payload);
      })
      .addCase(createTicket.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getCustomerCareList.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getCustomerCareList.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.customerCareList = action.payload;
      })
      .addCase(getCustomerCareList.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      });
  },
});

export default ticketSlice.reducer;
