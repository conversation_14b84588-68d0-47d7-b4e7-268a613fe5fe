import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { WebView } from 'react-native-webview';
import { ThemeContext } from '../context/ThemeContext';
import {
  validateCouponCode,
  validateGiftCard,
  createSubscription,
  verifyPayment,
  getRazorpayConfig,
} from '../redux/subscriptionSlice';
import { clearCart } from '../redux/cartSlice';
import CartSummary from '../components/CartSummary';
import WebViewPayment from '../components/WebViewPayment';

const PaymentScreen = ({ route, navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const { cartItems, cartTotal, cartItemCount, checkoutDetails } = route.params || {};
  const dispatch = useDispatch();
  const studentId = useSelector((state) => state.auth.user?.user?.id);
  
  // Determine if this is a cart payment or single package payment
  const isCartPayment = cartItems && cartItems.length > 0;
  const paymentItems = isCartPayment ? cartItems : [checkoutDetails];
  const totalAmount = isCartPayment ? cartTotal : checkoutDetails?.originalPrice || 0;
  
  const [couponCode, setCouponCode] = useState('');
  const [giftCardCode, setGiftCardCode] = useState('');
  const [giftCardPin, setGiftCardPin] = useState('');
  const [discount, setDiscount] = useState(0);
  const [discountType, setDiscountType] = useState('');
  const [couponMessage, setCouponMessage] = useState(null);
  const [giftCardMessage, setGiftCardMessage] = useState(null);
  const [isCouponApplied, setIsCouponApplied] = useState(false);
  const [isGiftCardApplied, setIsGiftCardApplied] = useState(false);
  const [loading, setLoading] = useState(false);
  const [applyingCoupon, setApplyingCoupon] = useState(false);
  const [applyingGiftCard, setApplyingGiftCard] = useState(false);
  const [showPaymentWebView, setShowPaymentWebView] = useState(false);
  const [paymentData, setPaymentData] = useState(null);

  const calculateFinalPrice = () => {
    if (!discount) return totalAmount;
    return discountType === 'amount'
      ? totalAmount - discount
      : totalAmount * (1 - discount / 100);
  };

  const handleValidateCoupon = async () => {
    if (!couponCode) return;
    setApplyingCoupon(true);
    setCouponMessage(null);

    try {
      const response = await dispatch(validateCouponCode({ coupon_code: couponCode })).unwrap();
      if (response.valid) {
        setDiscount(response.discount);
        setDiscountType(response.discount_type);
        setCouponMessage({ type: 'success', text: response.message });
        setIsCouponApplied(true);
        setIsGiftCardApplied(false);
      } else {
        setCouponMessage({ type: 'error', text: response.message });
      }
    } catch {
      setCouponMessage({ type: 'error', text: 'Invalid coupon code.' });
    } finally {
      setApplyingCoupon(false);
    }
  };

  const handleValidateGiftCard = async () => {
    if (!giftCardCode || !giftCardPin) return;
    setApplyingGiftCard(true);
    setGiftCardMessage(null);

    try {
      const response = await dispatch(validateGiftCard({
        gift_code: giftCardCode,
        pin: giftCardPin
      })).unwrap();

      if (response.valid) {
        setDiscount(response.amount);
        setDiscountType('amount');
        setGiftCardMessage({ type: 'success', text: response.message });
        setIsGiftCardApplied(true);
        setIsCouponApplied(false);
      } else {
        setGiftCardMessage({ type: 'error', text: response.message });
      }
    } catch {
      setGiftCardMessage({ type: 'error', text: 'Invalid gift card.' });
    } finally {
      setApplyingGiftCard(false);
    }
  };

  const clearCoupon = () => {
    setCouponCode('');
    setIsCouponApplied(false);
    setCouponMessage(null);
    setDiscount(0);
    setDiscountType('');
  };

  const clearGiftCard = () => {
    setGiftCardCode('');
    setGiftCardPin('');
    setIsGiftCardApplied(false);
    setGiftCardMessage(null);
    setDiscount(0);
    setDiscountType('');
  };

  const handlePayment = async () => {
    if (!paymentItems || paymentItems.length === 0) return;
    setLoading(true);

    try {
      console.log('🚀 Starting payment process...');

      // Step 1: Get Razorpay config
      console.log('📡 Getting Razorpay config...');
      const configRes = await dispatch(getRazorpayConfig()).unwrap();
      console.log('✅ Razorpay config received:', configRes);

      // For cart payments, we need to handle multiple items
      // For now, we'll create a subscription for the first package item
      // In a real app, you might need a different API endpoint for cart payments
      const firstPackageItem = isCartPayment
        ? paymentItems.find(item => item.type === 'package')
        : checkoutDetails;

      if (!firstPackageItem) {
        console.log('❌ No valid package found');
        Alert.alert('Error', 'No valid package found for payment');
        setLoading(false);
        return;
      }

      console.log('📦 Package item:', firstPackageItem);

      const subscriptionPayload = {
        student: studentId,
        package: firstPackageItem.packageId || firstPackageItem.id,
        start_date: new Date().toISOString().split('T')[0] + "T00:00:00+05:30",
        ...(isGiftCardApplied && { gift_card_code: giftCardCode, gift_card_pin: giftCardPin }),
        ...(isCouponApplied && { coupon: couponCode }),
        // Add cart-specific data if needed
        ...(isCartPayment && { cart_items: cartItems, cart_total: cartTotal }),
      };

      console.log('📝 Subscription payload:', subscriptionPayload);

      // Step 2: Create subscription
      console.log('🔄 Creating subscription...');
      const subscriptionRes = await dispatch(createSubscription(subscriptionPayload)).unwrap();
      console.log('✅ Subscription created:', subscriptionRes);

      const { razorpay_key, company_name, logo_url } = configRes;
      const { razorpay_order, final_price, currency, subscription_id } = subscriptionRes;

      // Step 3: Prepare payment data for WebView
      const webViewPaymentData = {
        razorpay_key,
        company_name,
        logo_url,
        razorpay_order,
        final_price,
        currency,
        subscription_id,
        description: isCartPayment ? 'Cart Purchase' : 'Package Subscription'
      };

      console.log('💳 Payment data prepared:', webViewPaymentData);
      console.log('🌐 Opening WebView payment modal...');

      setPaymentData(webViewPaymentData);
      setShowPaymentWebView(true);

    } catch (error) {
      console.error('❌ Payment initiation error:', error);
      console.error('❌ Error response:', error.response);
      console.error('❌ Error data:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      console.error('❌ Error headers:', error.response?.headers);

      // Check if it's an HTML error response
      if (typeof error.response?.data === 'string' && error.response.data.includes('<!DOCTYPE html>')) {
        console.error('❌ Server returned HTML error page instead of JSON');
        Alert.alert('Server Error', 'The server encountered an error. Please try again later or contact support.');
      } else {
        Alert.alert('Error', error?.message || error.response?.data?.message || 'Payment initiation failed');
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = async (data) => {
    try {
      await dispatch(verifyPayment({
        razorpay_order_id: data.razorpay_order_id,
        razorpay_payment_id: data.razorpay_payment_id,
        razorpay_signature: data.razorpay_signature,
        subscription_id: data.subscription_id,
        amount: data.amount,
      })).unwrap();

      // Clear cart if this was a cart payment
      if (isCartPayment) {
        dispatch(clearCart());
      }

      setShowPaymentWebView(false);

      // Navigate to success screen
      navigation.navigate('PaymentSuccessScreen', {
        paymentData: data,
        orderAmount: data.amount,
        isCartPayment,
        items: paymentItems,
      });
    } catch (error) {
      setShowPaymentWebView(false);
      navigation.navigate('PaymentFailureScreen', {
        error: 'Payment verification failed',
        canRetry: true,
      });
    }
  };

  const handlePaymentFailure = (error) => {
    setShowPaymentWebView(false);
    navigation.navigate('PaymentFailureScreen', {
      error: error?.error || 'Payment failed',
      canRetry: true,
    });
  };

  const handlePaymentClose = () => {
    setShowPaymentWebView(false);
  };

  const renderPaymentItems = () => {
    if (isCartPayment) {
      return (
        <CartSummary
          showCheckoutButton={false}
          appliedCoupon={isCouponApplied ? couponCode : null}
          appliedGiftCard={isGiftCardApplied ? giftCardCode : null}
          discount={discount}
          discountType={discountType}
        />
      );
    }

    // Render single package item
    return (
      <View style={[styles.card, isDarkMode && styles.cardDark]}>
        <Text style={[styles.packageName, isDarkMode && styles.textDark]}>
          {checkoutDetails?.packageName}
        </Text>
        <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
          What you get:
        </Text>
        {checkoutDetails?.descriptions?.map((description, index) => (
          <View key={index} style={styles.benefitItem}>
            <Icon 
              name="check-circle" 
              size={16} 
              color="#4CAF50" 
              style={styles.checkIcon} 
            />
            <Text style={[styles.benefitText, isDarkMode && styles.textDark]}>
              {description}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <ScrollView style={[styles.container, isDarkMode && styles.containerDark]}>
      <Text style={[styles.title, isDarkMode && styles.titleDark]}>
        {isCartPayment ? 'Complete Your Purchase' : 'Checkout'}
      </Text>

      {renderPaymentItems()}

      <View style={[styles.card, isDarkMode && styles.cardDark]}>
        <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
          Payment Details
        </Text>
        <Text style={[styles.text, isDarkMode && styles.textDark]}>
          Original Price: ₹{totalAmount}
        </Text>
        {discount > 0 && (
          <Text style={[styles.text, isDarkMode && styles.textDark]}>
            Discount: {discountType === "amount" ? `₹${discount}` : `${discount}%`}
          </Text>
        )}
        <Text style={[styles.finalPrice, isDarkMode && styles.textDark]}>
          Final Price: ₹{calculateFinalPrice()}
        </Text>

        {/* Coupon Code Section */}
        <View style={styles.couponContainer}>
          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.input, isDarkMode && styles.inputDark]}
              placeholder="Enter Coupon Code"
              placeholderTextColor={isDarkMode ? "#888" : "#666"}
              value={couponCode}
              onChangeText={setCouponCode}
              editable={!isGiftCardApplied && !applyingCoupon}
            />
            {couponCode && (
              <TouchableOpacity onPress={clearCoupon} style={styles.clearButton}>
                <Icon name="times" size={20} color={isDarkMode ? "#ff6b6b" : "#ff3d00"} />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity
            style={[
              styles.applyButton,
              (!couponCode || isGiftCardApplied || applyingCoupon) && styles.disabledButton
            ]}
            onPress={handleValidateCoupon}
            disabled={!couponCode || isGiftCardApplied || applyingCoupon}
          >
            {applyingCoupon ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.applyButtonText}>Apply</Text>
            )}
          </TouchableOpacity>
        </View>
        {couponMessage && (
          <View style={styles.messageContainer}>
            <Icon
              name={couponMessage.type === 'success' ? 'check-circle' : 'exclamation-circle'}
              size={16}
              color={couponMessage.type === 'success' ? '#00c853' : '#ff3d00'}
              style={styles.messageIcon}
            />
            <Text
              style={[
                styles.message,
                { color: couponMessage.type === 'success' ? '#00c853' : '#ff3d00' }
              ]}
            >
              {couponMessage.text}
            </Text>
          </View>
        )}

        {/* Gift Card Section */}
        <View style={styles.giftCardContainer}>
          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.input, isDarkMode && styles.inputDark]}
              placeholder="Enter Gift Card Code"
              placeholderTextColor={isDarkMode ? "#888" : "#666"}
              value={giftCardCode}
              onChangeText={setGiftCardCode}
              editable={!isCouponApplied && !applyingGiftCard}
            />
            <TextInput
              style={[styles.input, isDarkMode && styles.inputDark, { marginLeft: 8 }]}
              placeholder="PIN"
              placeholderTextColor={isDarkMode ? "#888" : "#666"}
              value={giftCardPin}
              onChangeText={setGiftCardPin}
              keyboardType="numeric"
              editable={!isCouponApplied && !applyingGiftCard}
            />
            {(giftCardCode || giftCardPin) && (
              <TouchableOpacity onPress={clearGiftCard} style={styles.clearButton}>
                <Icon name="times" size={20} color={isDarkMode ? "#ff6b6b" : "#ff3d00"} />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity
            style={[
              styles.applyButton2,
              (!giftCardCode || !giftCardPin || isCouponApplied || applyingGiftCard) && styles.disabledButton
            ]}
            onPress={handleValidateGiftCard}
            disabled={!giftCardCode || !giftCardPin || isCouponApplied || applyingGiftCard}
          >
            {applyingGiftCard ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.applyButtonText}>Apply</Text>
            )}
          </TouchableOpacity>
        </View>
        {giftCardMessage && (
          <View style={styles.messageContainer}>
            <Icon
              name={giftCardMessage.type === 'success' ? 'check-circle' : 'exclamation-circle'}
              size={16}
              color={giftCardMessage.type === 'success' ? '#00c853' : '#ff3d00'}
              style={styles.messageIcon}
            />
            <Text
              style={[
                styles.message,
                { color: giftCardMessage.type === 'success' ? '#00c853' : '#ff3d00' }
              ]}
            >
              {giftCardMessage.text}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.checkoutButton,
            loading && styles.disabledButton,
            isDarkMode && styles.checkoutButtonDark
          ]}
          onPress={handlePayment}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.checkoutButtonText}>Pay Now</Text>
          )}
        </TouchableOpacity>
      </View>

      <WebViewPayment
        visible={showPaymentWebView}
        onClose={handlePaymentClose}
        onSuccess={handlePaymentSuccess}
        onFailure={handlePaymentFailure}
        paymentData={paymentData}
        isDarkMode={isDarkMode}
      />
    </ScrollView>
  );
};

// Styles would be similar to the existing Checkout screen
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#00c853',
  },
  titleDark: {
    color: '#4CAF50',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardDark: {
    backgroundColor: '#1e1e1e',
    shadowColor: '#000',
    elevation: 3,
  },
  textDark: {
    color: '#fff',
  },
  packageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  text: {
    color: '#333',
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkIcon: {
    marginRight: 8,
    width: 16,
  },
  benefitText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  finalPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 16,
  },
  couponContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    paddingHorizontal: 12,
    backgroundColor: 'white',
    color: '#333',
  },
  inputDark: {
    backgroundColor: '#2c2c2c',
    borderColor: '#444',
    color: '#fff',
  },
  clearButton: {
    padding: 8,
    position: 'absolute',
    right: 8,
  },
  applyButton: {
    backgroundColor: '#00c853',
    padding: 10,
    borderRadius: 4,
    marginLeft: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  applyButton2: {
    backgroundColor: '#00c853',
    padding: 10,
    borderRadius: 4,
    marginTop: 10,
    minWidth: 80,
    alignItems: 'center',
  },
  applyButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  messageIcon: {
    marginRight: 8,
  },
  message: {
    fontSize: 14,
  },
  giftCardContainer: {
    marginBottom: 16,
  },
  checkoutButton: {
    backgroundColor: '#00c853',
    padding: 16,
    borderRadius: 4,
    alignItems: 'center',
  },
  checkoutButtonDark: {
    backgroundColor: '#4CAF50',
  },
});

export default PaymentScreen;
