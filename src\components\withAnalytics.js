import React, { useEffect } from 'react';
import { logScreenView, logEvent } from '../utils/analytics';

/**
 * Higher-Order Component for automatic analytics tracking
 * 
 * This HOC automatically tracks screen views and provides
 * analytics tracking capabilities to wrapped components.
 * 
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {string} screenName - The name of the screen for analytics
 * @param {string} screenClass - Optional screen class for analytics
 * @returns {React.Component} - Enhanced component with analytics
 */
const withAnalytics = (WrappedComponent, screenName, screenClass = null) => {
  const AnalyticsWrapper = (props) => {
    useEffect(() => {
      // Automatically log screen view when component mounts
      logScreenView(screenName, screenClass || `${screenName}Screen`);
      
      // Log screen enter event with additional context
      logEvent('screen_enter', {
        screen_name: screenName,
        screen_class: screenClass || `${screenName}Screen`,
        timestamp: new Date().toISOString(),
        navigation_params: props.route?.params ? Object.keys(props.route.params) : [],
      });

      // Cleanup function to log screen exit
      return () => {
        logEvent('screen_exit', {
          screen_name: screenName,
          screen_class: screenClass || `${screenName}Screen`,
          timestamp: new Date().toISOString(),
        });
      };
    }, []);

    // Enhanced props with analytics helpers
    const enhancedProps = {
      ...props,
      analytics: {
        logEvent: (eventName, parameters = {}) => {
          logEvent(eventName, {
            ...parameters,
            screen: screenName,
            timestamp: new Date().toISOString(),
          });
        },
        logScreenView: (customScreenName, customScreenClass) => {
          logScreenView(
            customScreenName || screenName,
            customScreenClass || screenClass || `${screenName}Screen`
          );
        },
      },
    };

    return <WrappedComponent {...enhancedProps} />;
  };

  // Set display name for debugging
  AnalyticsWrapper.displayName = `withAnalytics(${WrappedComponent.displayName || WrappedComponent.name})`;

  return AnalyticsWrapper;
};

export default withAnalytics;
