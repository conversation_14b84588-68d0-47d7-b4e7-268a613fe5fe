import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';

export const SkeletonCard = ({ isDarkMode }) => {
  const shimmerValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, []);

  const opacity = shimmerValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <View style={[styles.card, isDarkMode && styles.cardDark]}>
      <View style={styles.header}>
        <Animated.View style={[styles.badge, { opacity }]} />
        <Animated.View style={[styles.badge, { opacity, width: 40 }]} />
      </View>
      <View style={styles.content}>
        <Animated.View style={[styles.title, { opacity }]} />
        <Animated.View style={[styles.text, { opacity }]} />
        <View style={styles.buttonContainer}>
          <Animated.View style={[styles.button, { opacity }]} />
          <Animated.View style={[styles.button, { opacity, width: '30%' }]} />
        </View>
      </View>
      <View style={[styles.footer, isDarkMode && styles.footerDark]}>
        <Animated.View style={[styles.footerText, { opacity }]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 15,
    borderRadius: 8,
    backgroundColor: '#fff',
    elevation: 2,
    margin: 8,
  },
  cardDark: {
    backgroundColor: '#2d2d2d',
  },
  header: {
    padding: 10,
    flexDirection: 'row',
    gap: 10,
  },
  badge: {
    height: 24,
    width: 80,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
  },
  content: {
    padding: 10,
  },
  title: {
    height: 20,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 8,
  },
  text: {
    height: 16,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 10,
    width: '60%',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  },
  button: {
    height: 32,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    width: '60%',
  },
  footer: {
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  footerDark: {
    backgroundColor: '#1e1e1e',
  },
  footerText: {
    height: 16,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    width: '40%',
  },
});
