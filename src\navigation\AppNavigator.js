import React, { useContext } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ThemeContext } from '../context/ThemeContext';
import AuthNavigator from './AuthNavigator';
import DrawerNavigator from './DrawerNavigator';
import WalkAroundScreen from '../screens/WalkAroundScreen';
import { useSelector } from 'react-redux';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const { isAuthenticated, hasCompletedWalkthrough } = useSelector((state) => state.auth);

  return (<Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
        },
        headerTintColor: isDarkMode ? '#fff' : '#000',
      }}
    >
      {!isAuthenticated ? (
        <Stack.Screen
          name="Auth"
          component={AuthNavigator}
          options={{ headerShown: false }}
        />      ) : (
        <Stack.Group>
          {!hasCompletedWalkthrough ? (
            <Stack.Screen
              name="WalkAround"
              component={WalkAroundScreen}
              options={{
                headerShown: true,
                headerBackVisible: true, // Ensures back button is visible
                title: 'Walk Around', // Optional: set a title
              }}
            />
          ) : (
            <Stack.Screen
              name="Main"
              component={DrawerNavigator}
              options={{ headerShown: false }}
            />
          )}
        </Stack.Group>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
