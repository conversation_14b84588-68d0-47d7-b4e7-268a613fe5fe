import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';

const EventDetailModal = ({ 
  visible, 
  onClose, 
  event, 
  getCategoryColor, 
  getStatusColor, 
  formatDate 
}) => {
  const { isDarkMode } = useContext(ThemeContext);

  if (!event) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        {/* Header */}
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>
            {event.title}
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialIcons 
              name="close" 
              size={24} 
              color={isDarkMode ? '#fff' : '#333'} 
            />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Event Image */}
          <Image source={{ uri: event.image }} style={styles.eventImage} />

          {/* Badges */}
          <View style={styles.badgeContainer}>
            <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(event.category) }]}>
              <Text style={styles.badgeText}>{event.category.toUpperCase()}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(event.status) }]}>
              <Text style={styles.badgeText}>{event.status.toUpperCase()}</Text>
            </View>
          </View>

          {/* Description */}
          <Text style={[styles.description, isDarkMode && styles.descriptionDark]}>
            {event.description}
          </Text>

          {/* Event Details */}
          <View style={styles.detailsContainer}>
            <View style={styles.detailsRow}>
              <View style={styles.detailsColumn}>
                <View style={styles.detailItem}>
                  <MaterialIcons name="event" size={20} color="#28a745" />
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, isDarkMode && styles.detailLabelDark]}>
                      Date:
                    </Text>
                    <Text style={[styles.detailValue, isDarkMode && styles.detailValueDark]}>
                      {formatDate(event.date)}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailItem}>
                  <MaterialIcons name="access-time" size={20} color="#28a745" />
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, isDarkMode && styles.detailLabelDark]}>
                      Time:
                    </Text>
                    <Text style={[styles.detailValue, isDarkMode && styles.detailValueDark]}>
                      {event.time}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.detailsColumn}>
                <View style={styles.detailItem}>
                  <MaterialIcons name="location-on" size={20} color="#28a745" />
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, isDarkMode && styles.detailLabelDark]}>
                      Location:
                    </Text>
                    <Text style={[styles.detailValue, isDarkMode && styles.detailValueDark]}>
                      {event.location}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailItem}>
                  <MaterialIcons name="people" size={20} color="#28a745" />
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, isDarkMode && styles.detailLabelDark]}>
                      Attendees:
                    </Text>
                    <Text style={[styles.detailValue, isDarkMode && styles.detailValueDark]}>
                      {event.attendees}/{event.maxAttendees}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* Organizer */}
          <View style={styles.organizerContainer}>
            <Text style={[styles.organizerLabel, isDarkMode && styles.organizerLabelDark]}>
              Organizer:
            </Text>
            <Text style={[styles.organizerValue, isDarkMode && styles.organizerValueDark]}>
              {event.organizer}
            </Text>
          </View>

          {/* Registration Required */}
          <View style={styles.registrationContainer}>
            <Text style={[styles.registrationLabel, isDarkMode && styles.registrationLabelDark]}>
              Registration Required:
            </Text>
            <Text style={[styles.registrationValue, isDarkMode && styles.registrationValueDark]}>
              {event.registrationRequired ? 'Yes' : 'No'}
            </Text>
          </View>

          {/* Tags */}
          <View style={styles.tagsContainer}>
            <Text style={[styles.tagsLabel, isDarkMode && styles.tagsLabelDark]}>
              Tags:
            </Text>
            <View style={styles.tagsRow}>
              {event.tags?.map((tag, index) => (
                <View key={index} style={[styles.tag, isDarkMode && styles.tagDark]}>
                  <Text style={[styles.tagText, isDarkMode && styles.tagTextDark]}>
                    {tag}
                  </Text>
                </View>
              ))}
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={[styles.closeActionButton, isDarkMode && styles.closeActionButtonDark]}
              onPress={onClose}
            >
              <Text style={[styles.closeActionButtonText, isDarkMode && styles.closeActionButtonTextDark]}>
                Close
              </Text>
            </TouchableOpacity>

            {event.status === 'upcoming' && event.registrationRequired && (
              <TouchableOpacity style={styles.registerButton}>
                <Text style={styles.registerButtonText}>Register Now</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#2a2a2a',
    borderBottomColor: '#444',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 16,
  },
  headerTitleDark: {
    color: '#fff',
  },
  closeButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  eventImage: {
    width: '100%',
    height: 250,
    borderRadius: 12,
    marginBottom: 16,
  },
  badgeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  description: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 24,
  },
  descriptionDark: {
    color: '#fff',
  },
  detailsContainer: {
    marginBottom: 24,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailsColumn: {
    flex: 1,
    marginRight: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  detailContent: {
    marginLeft: 12,
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  detailLabelDark: {
    color: '#fff',
  },
  detailValue: {
    fontSize: 14,
    color: '#666',
  },
  detailValueDark: {
    color: '#ccc',
  },
  organizerContainer: {
    marginBottom: 16,
  },
  organizerLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  organizerLabelDark: {
    color: '#fff',
  },
  organizerValue: {
    fontSize: 14,
    color: '#666',
  },
  organizerValueDark: {
    color: '#ccc',
  },
  registrationContainer: {
    marginBottom: 16,
  },
  registrationLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  registrationLabelDark: {
    color: '#fff',
  },
  registrationValue: {
    fontSize: 14,
    color: '#666',
  },
  registrationValueDark: {
    color: '#ccc',
  },
  tagsContainer: {
    marginBottom: 32,
  },
  tagsLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  tagsLabelDark: {
    color: '#fff',
  },
  tagsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  tagDark: {
    backgroundColor: '#2a2a2a',
    borderColor: '#444',
  },
  tagText: {
    fontSize: 12,
    color: '#333',
  },
  tagTextDark: {
    color: '#fff',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  closeActionButton: {
    flex: 1,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#6c757d',
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  closeActionButtonDark: {
    borderColor: '#666',
  },
  closeActionButtonText: {
    fontSize: 16,
    color: '#6c757d',
    fontWeight: '600',
  },
  closeActionButtonTextDark: {
    color: '#ccc',
  },
  registerButton: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#28a745',
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  registerButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
});

export default EventDetailModal;
