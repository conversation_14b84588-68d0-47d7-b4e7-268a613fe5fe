import React, { useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';
import { Card } from 'react-native-paper';
import { toggleSaveTestSeries } from '../redux/dashboardSlice';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import HeaderWithBack from '../components/HeaderWithBack';

export default function TestSeriesDetailsScreen({ route, navigation }) {
  const { isDarkMode } = useContext(ThemeContext);
  const { testSeries } = route.params;
  const { isLoading, error } = useSelector((state) => state.dashboard);

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={styles.centered}>
          <ActivityIndicator size="large" color={isDarkMode ? '#fff' : '#000'} />
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={styles.centered}>
          <Text style={[styles.errorText, isDarkMode && styles.textDark]}>
            {error}
          </Text>
          <TouchableOpacity 
            style={styles.retryButton} 
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <HeaderWithBack title="Test Series Details" navigation={navigation} isDarkMode={isDarkMode} />
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Card style={[styles.card, isDarkMode && styles.cardDark]}>
            <Card.Content>            <View style={styles.titleContainer}>
                <Text style={[styles.title, isDarkMode && styles.textDark]}>
                  {testSeries.title}
                </Text>
              </View>
              <Text style={[styles.description, isDarkMode && styles.textDark]}>
                {testSeries.description}
              </Text>
              <View style={styles.detailsContainer}>
                <View style={styles.detailItem}>
                  <Text style={[styles.label, isDarkMode && styles.textDark]}>Subject</Text>
                  <Text style={[styles.value, isDarkMode && styles.textDark]}>
                    {testSeries.subject}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={[styles.label, isDarkMode && styles.textDark]}>Questions</Text>
                  <Text style={[styles.value, isDarkMode && styles.textDark]}>
                    {testSeries.totalQuestions}
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={[styles.label, isDarkMode && styles.textDark]}>Time</Text>
                  <Text style={[styles.value, isDarkMode && styles.textDark]}>
                    {testSeries.duration} minutes
                  </Text>
                </View>
              </View>
            </Card.Content>
            <Card.Actions>
              <TouchableOpacity 
                style={[styles.button, styles.startButton]} 
                onPress={() => {
                  Alert.alert(
                    'Start Test',
                    'Are you ready to begin the test?',
                    [
                      {
                        text: 'Cancel',
                        style: 'cancel',
                      },
                      {
                        text: 'Start',
                        onPress: () => {
                          // TODO: Implement test start logic
                          Alert.alert('Starting test...', 'This feature will be implemented soon.');
                        },
                      },
                    ],
                    { cancelable: true }
                  );
                }}
              >
                <Text style={styles.buttonText}>Start Test</Text>
              </TouchableOpacity>
            </Card.Actions>
          </Card>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  scrollContent: {
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    marginVertical: 8,
    backgroundColor: '#ffffff',
    elevation: 4,
  },
  cardDark: {
    backgroundColor: '#1e1e1e',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#000000',
  },
  description: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 16,
  },
  textDark: {
    color: '#ffffff',
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  detailItem: {
    flex: 1,
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  startButton: {
    backgroundColor: '#007AFF',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  }
});
