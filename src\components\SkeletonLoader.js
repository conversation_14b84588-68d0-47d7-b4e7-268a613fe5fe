import React, { useContext } from 'react';
import { View, Dimensions, StyleSheet } from 'react-native';
import Animated, { 
  useAnimatedStyle, 
  withRepeat, 
  withSequence, 
  withTiming,
  useSharedValue, 
  withDelay,
  FadeIn
} from 'react-native-reanimated';
import { ThemeContext } from '../context/ThemeContext';

export const SkeletonLoader = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const opacity = useSharedValue(0.3);

  React.useEffect(() => {
    opacity.value = withRepeat(
      withSequence(
        withTiming(0.7, { duration: 1000 }),
        withTiming(0.3, { duration: 1000 })
      ),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View 
      style={[
        styles.card, 
        isDarkMode && styles.cardDark,
        animatedStyle
      ]}
      entering={FadeIn.duration(500)}
    >
      <View style={[styles.image, isDarkMode && styles.imageDark]} />
      <View style={styles.content}>
        <View style={[styles.title, isDarkMode && styles.titleDark]} />
        <View style={styles.textContainer}>
          <View style={[styles.text, isDarkMode && styles.textDark]} />
          <View style={[styles.text, isDarkMode && styles.textDark]} />
          <View style={[styles.text, isDarkMode && styles.textDark, { width: '60%' }]} />
        </View>
        <View style={styles.footer}>
          <View style={[styles.footerItem, isDarkMode && styles.footerItemDark]} />
          <View style={[styles.footerItem, isDarkMode && styles.footerItemDark]} />
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 150,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#eeeeee',
  },
  cardDark: {
    backgroundColor: '#121212',
    borderBottomColor: '#333333',
  },
  image: {
    width: '100%',
    height: '35%',
    backgroundColor: '#f0f0f0',
  },
  imageDark: {
    backgroundColor: '#2a2a2a',
  },
  content: {
    padding: 20,
    flex: 1,
  },
  title: {
    height: 24,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginBottom: 20,
  },
  titleDark: {
    backgroundColor: '#2a2a2a',
  },
  textContainer: {
    flex: 1,
    gap: 12,
  },
  text: {
    height: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
  },
  textDark: {
    backgroundColor: '#2a2a2a',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#eeeeee',
  },
  footerItem: {
    width: 80,
    height: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
  },
  footerItemDark: {
    backgroundColor: '#2a2a2a',
  },
});
