import React from 'react';
import { Modal, View, StyleSheet, Image, TouchableOpacity, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { setHasSeenWelcomePopup } from '../redux/popupSlice';
import Icon from 'react-native-vector-icons/FontAwesome';

export const PassPopup = ({ visible, onClose }) => {
  const dispatch = useDispatch();

  const handleClose = () => {
    dispatch(setHasSeenWelcomePopup(true));
    onClose();
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        onPress={handleClose}
        activeOpacity={1}
      >
        <View style={styles.modalContent}>
          {/* Cross Icon */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            activeOpacity={0.7}
          >
            <Icon name="times" size={24} color="#fff" />
          </TouchableOpacity>

          {/* Image Container */}
          <TouchableOpacity
            style={styles.imageContainer}
            onPress={handleClose}
            activeOpacity={1}
          >
            <Image
              source={require('../../assets/pass.png')}
              style={styles.image}
              resizeMode="contain"
            />
          </TouchableOpacity>

          {/* Instruction Text */}
          <Text style={styles.instructionText}>
            Tap anywhere or press the cross button to hide this
          </Text>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'transparent',
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: '100%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  instructionText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 20,
    opacity: 0.8,
  }
});
