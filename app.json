{
  "expo": {
    "name": "Shashtrarth",
    "slug": "shashtrarth",
    "version": "1.0.2",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "light",
    "newArchEnabled": true,
    "jsEngine": "hermes",
    "primaryColor": "#000000",
    "assetBundlePatterns": [
      "**/*"
    ],
    "packagerOpts": {
      "config": "metro.config.js"
    },
    "splash": {
      "image": "./assets/splash.mp4",
      "resizeMode": "cover",
      "backgroundColor": "#000000"
    },
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.shashtrarth"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      },
      "edgeToEdgeEnabled": true,
      "package": "com.shashtrarth",
      "googleServicesFile": "./google-services.json"
    },
    "web": {
      "favicon": "./assets/icon.png"
    },
    "plugins": [
      "expo-font",
      // "expo-firebase-analytics", // Temporarily disabled to fix IOException
      [
        "expo-auth-session",
        {
          "scheme": "com.shashtrarth"
        }
      ],
      [
        "expo-dev-client",
        {
          "addGeneratedScheme": false
        }
      ]
    ],
    "updates": {
      "enabled": false,
      "checkAutomatically": "NEVER",
      "fallbackToCacheTimeout": 0
    },
    "extra": {
      "eas": {
        "projectId": "your-project-id-here"
      }
    },
    "scheme": "com.shashtrarth"
  }
}
