import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Helper to get token
const getAuthToken = (getState) => {
  const state = getState();
  return state?.auth?.JWT_Token?.access;
};

// POST chat message
export const sendChatMessage = createAsyncThunk(
  "chatBot/sendMessage",
  async (messageData, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error("No access token found");
      }
      const response = await axios.post(
        `${baseURL}api/chat-ai/ai-chatbot/`,
        messageData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message || "Failed to send message");
    }
  }
);

// GET chat history
export const getChatHistory = createAsyncThunk(
  "chatBot/getHistory",
  async (_, { getState, rejectWithValue }) => {
    try {
      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error("No access token found");
      }
      const response = await axios.get(
        `${baseURL}api/chat/history/`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message || "Failed to fetch chat history");
    }
  }
);

// Slice
const chatBotSlice = createSlice({
  name: "chatBot",
  initialState: {
    loading: false,
    error: null,
    messages: [],
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Send Message
      .addCase(sendChatMessage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.loading = false;
        state.messages.push(action.payload);
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Chat History
      .addCase(getChatHistory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getChatHistory.fulfilled, (state, action) => {
        state.loading = false;
        state.messages = action.payload;
      })
      .addCase(getChatHistory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default chatBotSlice.reducer;
