import React, { useContext } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';

const PracticeStatsComponent = ({ 
  dailyPractice = 0, 
  weeklyPractice = 0, 
  monthlyPractice = 0, 
  continuousPractice = 0,
  title = "Practice Statistics",
  showTitle = true 
}) => {
  const { isDarkMode } = useContext(ThemeContext);

  const practiceData = [
    {
      title: 'Daily Practice',
      value: dailyPractice,
      icon: 'calendar-today',
      color: isDarkMode ? '#5a9ff2' : '#4a90e2',
      borderColor: '#4a90e2'
    },
    {
      title: 'Weekly Practice',
      value: weeklyPractice,
      icon: 'calendar-week',
      color: isDarkMode ? '#3edd81' : '#2ecc71',
      borderColor: '#2ecc71'
    },
    {
      title: 'Monthly Practice',
      value: monthlyPractice,
      icon: 'calendar-month',
      color: isDarkMode ? '#f2d520' : '#f1c40f',
      borderColor: '#f1c40f'
    },
    {
      title: 'Continuous Practice',
      value: continuousPractice,
      icon: 'infinity',
      color: isDarkMode ? '#f75d4d' : '#e74c3c',
      borderColor: '#e74c3c'
    }
  ];

  return (
    <View style={styles.container}>
      {showTitle && (
        <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
          {title}
        </Text>
      )}
      
      <View style={styles.statsContainer}>
        {practiceData.map((stat, index) => (
          <View 
            key={index} 
            style={[
              styles.statCard, 
              isDarkMode && styles.statCardDark,
              { borderColor: stat.borderColor }
            ]}
          >
            <MaterialCommunityIcons 
              name={stat.icon} 
              size={30} 
              color={stat.color} 
            />
            <Text style={[styles.statTitle, isDarkMode && styles.statTitleDark]}>
              {stat.title}
            </Text>
            <Text style={[styles.statValue, isDarkMode && styles.statValueDark]}>
              {stat.value}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#333',
  },
  textDark: {
    color: '#fff',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
  },
  statCardDark: {
    backgroundColor: '#1e1e1e',
  },
  statTitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  statTitleDark: {
    color: '#aaa',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  statValueDark: {
    color: '#fff',
  },
});

export default PracticeStatsComponent;
