import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Function to get Auth Token from Redux store
const getAuthToken = (getState) => {
  const state = getState();
  return {
    accessToken: state?.auth?.JWT_Token?.access,
    refreshToken: state?.auth?.JWT_Token?.refresh,
  };
};

// Get All Test Series Thunk (with Auth)
export const getTestSeries = createAsyncThunk(
  'dashboard/getTestSeries',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);
      const response = await axios.get(
        `${baseURL}api/questions/test-series-card/`,  // Update this endpoint as per your API
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching test series');
    }
  }
);

// Get Test Series by Sub-Course Slug Thunk (with Auth)
export const getTestSeriesBySubCourse = createAsyncThunk(
  "dashboard/getTestSeriesBySubCourse",
  async (slug, { getState, rejectWithValue }) => {
    try {
      const { accessToken } = getAuthToken(getState);

      const response = await axios.get(
        `${baseURL}/api/test-series/${slug}/`,  // Update this endpoint as per your API
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.data?.testSeries) {
        return response.data.testSeries;
      } else {
        return rejectWithValue("No test series data found.");
      }
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Error fetching test series for sub-course"
      );
    }
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState: {
    isLoading: false,
    error: null,
    currentTestSeries: null,
    testSeries: [],
    savedTestSeries: []
  },
  reducers: {    toggleSaveTestSeries: (state, action) => {
      const testSeries = action.payload;
      const isTestSeriesSaved = state.savedTestSeries.some(saved => saved.subcourse_slug === testSeries.subcourse_slug);
      
      if (isTestSeriesSaved) {
        state.savedTestSeries = state.savedTestSeries.filter(saved => saved.subcourse_slug !== testSeries.subcourse_slug);
      } else {
        state.savedTestSeries.push(testSeries);
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Get All Test Series
      .addCase(getTestSeries.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTestSeries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.testSeries = action.payload?.data || [];
      })
      .addCase(getTestSeries.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.testSeries = [];
      })
      // Get Test Series by Sub-Course Slug
      .addCase(getTestSeriesBySubCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTestSeriesBySubCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTestSeries = action.payload;
      })
      .addCase(getTestSeriesBySubCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { toggleSaveTestSeries } = dashboardSlice.actions;
export default dashboardSlice.reducer;
