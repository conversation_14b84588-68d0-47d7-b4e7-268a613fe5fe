import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';

const QuantitySelector = ({ 
  quantity, 
  onIncrease, 
  onDecrease, 
  minQuantity = 1, 
  maxQuantity = 99,
  disabled = false,
  size = 'medium' // 'small', 'medium', 'large'
}) => {
  const { isDarkMode } = useContext(ThemeContext);

  const sizeStyles = {
    small: {
      container: { height: 32 },
      button: { width: 32, height: 32 },
      text: { fontSize: 14 },
      icon: 14,
    },
    medium: {
      container: { height: 40 },
      button: { width: 40, height: 40 },
      text: { fontSize: 16 },
      icon: 16,
    },
    large: {
      container: { height: 48 },
      button: { width: 48, height: 48 },
      text: { fontSize: 18 },
      icon: 18,
    },
  };

  const currentSize = sizeStyles[size];

  const handleDecrease = () => {
    if (!disabled && quantity > minQuantity) {
      onDecrease();
    }
  };

  const handleIncrease = () => {
    if (!disabled && quantity < maxQuantity) {
      onIncrease();
    }
  };

  const canDecrease = !disabled && quantity > minQuantity;
  const canIncrease = !disabled && quantity < maxQuantity;

  return (
    <View style={[
      styles.container,
      currentSize.container,
      isDarkMode && styles.containerDark,
      disabled && styles.containerDisabled
    ]}>
      <TouchableOpacity
        style={[
          styles.button,
          styles.decreaseButton,
          currentSize.button,
          isDarkMode && styles.buttonDark,
          !canDecrease && styles.buttonDisabled
        ]}
        onPress={handleDecrease}
        disabled={!canDecrease}
        activeOpacity={0.7}
      >
        <Icon 
          name="minus" 
          size={currentSize.icon} 
          color={
            !canDecrease 
              ? (isDarkMode ? '#555' : '#ccc')
              : (isDarkMode ? '#fff' : '#333')
          } 
        />
      </TouchableOpacity>

      <View style={[styles.quantityContainer, currentSize.container]}>
        <Text style={[
          styles.quantityText,
          currentSize.text,
          isDarkMode && styles.quantityTextDark,
          disabled && styles.quantityTextDisabled
        ]}>
          {quantity}
        </Text>
      </View>

      <TouchableOpacity
        style={[
          styles.button,
          styles.increaseButton,
          currentSize.button,
          isDarkMode && styles.buttonDark,
          !canIncrease && styles.buttonDisabled
        ]}
        onPress={handleIncrease}
        disabled={!canIncrease}
        activeOpacity={0.7}
      >
        <Icon 
          name="plus" 
          size={currentSize.icon} 
          color={
            !canIncrease 
              ? (isDarkMode ? '#555' : '#ccc')
              : (isDarkMode ? '#fff' : '#333')
          } 
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  containerDark: {
    backgroundColor: '#2c2c2c',
    borderColor: '#444',
  },
  containerDisabled: {
    opacity: 0.6,
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  buttonDark: {
    backgroundColor: 'transparent',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  decreaseButton: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  increaseButton: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  quantityContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#e0e0e0',
  },
  quantityText: {
    fontWeight: '600',
    color: '#333',
  },
  quantityTextDark: {
    color: '#fff',
  },
  quantityTextDisabled: {
    color: '#999',
  },
});

export default QuantitySelector;
