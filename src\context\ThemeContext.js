import React, { createContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from 'react-native';
import { lightTheme, darkTheme } from '../theme';

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [themeMode, setThemeMode] = useState('system'); // 'system', 'light', 'dark'

  useEffect(() => {
    loadThemePreference();
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    if (themeMode === 'system') {
      setIsDarkMode(systemColorScheme === 'dark');
    }
  }, [systemColorScheme, themeMode]);

  const loadThemePreference = async () => {
    try {
      const savedThemeMode = await AsyncStorage.getItem('themeMode');
      const savedIsDarkMode = await AsyncStorage.getItem('isDarkMode');

      if (savedThemeMode !== null) {
        const mode = savedThemeMode;
        setThemeMode(mode);

        if (mode === 'system') {
          setIsDarkMode(systemColorScheme === 'dark');
        } else if (mode === 'dark') {
          setIsDarkMode(true);
        } else {
          setIsDarkMode(false);
        }
      } else if (savedIsDarkMode !== null) {
        // Migration: if old format exists, convert to new format
        const oldIsDark = JSON.parse(savedIsDarkMode);
        const newMode = oldIsDark ? 'dark' : 'light';
        setThemeMode(newMode);
        setIsDarkMode(oldIsDark);
        // Save in new format
        await AsyncStorage.setItem('themeMode', newMode);
        await AsyncStorage.removeItem('isDarkMode'); // Remove old format
      } else {
        // If no saved preference, use system preference
        setThemeMode('system');
        setIsDarkMode(systemColorScheme === 'dark');
        await AsyncStorage.setItem('themeMode', 'system');
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
      // If error loading preference, use system preference
      setThemeMode('system');
      setIsDarkMode(systemColorScheme === 'dark');
    }
  };

  const toggleTheme = async () => {
    try {
      let newMode;
      if (themeMode === 'system') {
        // If currently system, toggle to opposite of current system theme
        newMode = systemColorScheme === 'dark' ? 'light' : 'dark';
      } else if (themeMode === 'light') {
        newMode = 'dark';
      } else {
        newMode = 'light';
      }

      setThemeMode(newMode);
      setIsDarkMode(newMode === 'dark');
      await AsyncStorage.setItem('themeMode', newMode);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const setSystemTheme = async () => {
    try {
      setThemeMode('system');
      setIsDarkMode(systemColorScheme === 'dark');
      await AsyncStorage.setItem('themeMode', 'system');
    } catch (error) {
      console.error('Error setting system theme:', error);
    }
  };

  const setLightTheme = async () => {
    try {
      setThemeMode('light');
      setIsDarkMode(false);
      await AsyncStorage.setItem('themeMode', 'light');
    } catch (error) {
      console.error('Error setting light theme:', error);
    }
  };

  const setDarkTheme = async () => {
    try {
      setThemeMode('dark');
      setIsDarkMode(true);
      await AsyncStorage.setItem('themeMode', 'dark');
    } catch (error) {
      console.error('Error setting dark theme:', error);
    }
  };
  const theme = isDarkMode ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider value={{
      isDarkMode,
      themeMode,
      toggleTheme,
      setSystemTheme,
      setLightTheme,
      setDarkTheme,
      theme
    }}>
      {children}
    </ThemeContext.Provider>
  );
};
