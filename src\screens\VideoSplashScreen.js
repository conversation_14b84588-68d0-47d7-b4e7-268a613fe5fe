import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Dimensions, SafeAreaView } from 'react-native';
import { Video, ResizeMode } from 'expo-av';

export default function VideoSplashScreen({ onFinish }) {
  const videoRef = useRef(null);
  const [error, setError] = useState(false);

  useEffect(() => {
    loadAndPlayVideo();
  }, []);

  const loadAndPlayVideo = async () => {
    try {
      if (videoRef.current) {
        await videoRef.current.loadAsync(
          require('../../assets/splash.mp4'),
          {},
          false
        );
        await videoRef.current.playAsync();
      }
    } catch (err) {
      console.error('Error playing splash video:', err);
      setError(true);
      onFinish(); // Finish anyway if there's an error
    }
  };

  // If there's an error, we can skip showing the video
  if (error) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <Video
        ref={videoRef}
        style={styles.video}
        resizeMode={ResizeMode.COVER}
        isLooping={false}
        shouldPlay={true}
        onError={(error) => {
          console.error('Video error:', error);
          setError(true);
          onFinish();
        }}
        onPlaybackStatusUpdate={status => {
          if (status.isLoaded && status.didJustFinish) {
            onFinish();
          }
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: '100%',
    height: '100%',
  },
});
