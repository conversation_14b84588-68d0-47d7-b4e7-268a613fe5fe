import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';
import { editStudentProfile, getStudentProfile } from '../redux/authSlice';
import Toast from 'react-native-toast-message';

const EditProfileModal = ({ visible, onClose, profileData }) => {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const { loading } = useSelector((state) => state.auth);

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    phone: '',
    course: '',
  });

  const [errors, setErrors] = useState({});
  const [courses] = useState([
    'Banking',
    'SSC',
    'Railway',
    'UPSC',
    'State PSC',
    'Teaching',
    'Defense',
    'Other'
  ]);

  // Initialize form data when profile data changes
  useEffect(() => {
    if (profileData?.student) {
      const { user, phone, course } = profileData.student;
      setFormData({
        username: user?.username || '',
        firstName: user?.first_name || '',
        lastName: user?.last_name || '',
        phone: phone || '',
        course: course || '',
      });
    }
  }, [profileData]);

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = 'Enter a valid 10-digit phone number';
    }

    if (!formData.course.trim()) {
      newErrors.course = 'Course selection is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    const editData = {
      user: {
        username: formData.username,
        email: profileData?.student?.user?.email,
        first_name: formData.firstName,
        last_name: formData.lastName,
      },
      phone: formData.phone,
      course: formData.course,
    };

    try {
      const result = await dispatch(editStudentProfile({
        profileData: editData,
        id: profileData?.student?.id
      }));

      if (result.meta.requestStatus === 'fulfilled') {
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Profile updated successfully!',
        });
        
        // Refresh profile data
        if (profileData?.student?.id) {
          dispatch(getStudentProfile({ id: profileData.student.id }));
        }
        
        onClose();
      } else {
        throw new Error(result.payload || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Edit profile error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to update profile',
      });
    }
  };

  // Handle input change
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        {/* Header */}
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>
            Edit Profile
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialIcons 
              name="close" 
              size={24} 
              color={isDarkMode ? '#fff' : '#333'} 
            />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Username Field */}
          <View style={styles.fieldContainer}>
            <Text style={[styles.label, isDarkMode && styles.labelDark]}>
              Username *
            </Text>
            <TextInput
              style={[
                styles.input,
                isDarkMode && styles.inputDark,
                errors.username && styles.inputError
              ]}
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              placeholder="Enter username"
              placeholderTextColor={isDarkMode ? '#888' : '#999'}
            />
            {errors.username && (
              <Text style={styles.errorText}>{errors.username}</Text>
            )}
          </View>

          {/* First Name Field */}
          <View style={styles.fieldContainer}>
            <Text style={[styles.label, isDarkMode && styles.labelDark]}>
              First Name *
            </Text>
            <TextInput
              style={[
                styles.input,
                isDarkMode && styles.inputDark,
                errors.firstName && styles.inputError
              ]}
              value={formData.firstName}
              onChangeText={(value) => handleInputChange('firstName', value)}
              placeholder="Enter first name"
              placeholderTextColor={isDarkMode ? '#888' : '#999'}
            />
            {errors.firstName && (
              <Text style={styles.errorText}>{errors.firstName}</Text>
            )}
          </View>

          {/* Last Name Field */}
          <View style={styles.fieldContainer}>
            <Text style={[styles.label, isDarkMode && styles.labelDark]}>
              Last Name *
            </Text>
            <TextInput
              style={[
                styles.input,
                isDarkMode && styles.inputDark,
                errors.lastName && styles.inputError
              ]}
              value={formData.lastName}
              onChangeText={(value) => handleInputChange('lastName', value)}
              placeholder="Enter last name"
              placeholderTextColor={isDarkMode ? '#888' : '#999'}
            />
            {errors.lastName && (
              <Text style={styles.errorText}>{errors.lastName}</Text>
            )}
          </View>

          {/* Phone Field */}
          <View style={styles.fieldContainer}>
            <Text style={[styles.label, isDarkMode && styles.labelDark]}>
              Phone Number *
            </Text>
            <TextInput
              style={[
                styles.input,
                isDarkMode && styles.inputDark,
                errors.phone && styles.inputError
              ]}
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              placeholder="Enter 10-digit phone number"
              placeholderTextColor={isDarkMode ? '#888' : '#999'}
              keyboardType="numeric"
              maxLength={10}
            />
            {errors.phone && (
              <Text style={styles.errorText}>{errors.phone}</Text>
            )}
          </View>

          {/* Course Field */}
          <View style={styles.fieldContainer}>
            <Text style={[styles.label, isDarkMode && styles.labelDark]}>
              Course *
            </Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.courseContainer}
            >
              {courses.map((course) => (
                <TouchableOpacity
                  key={course}
                  style={[
                    styles.courseChip,
                    formData.course === course && styles.courseChipSelected,
                    isDarkMode && styles.courseChipDark,
                    formData.course === course && isDarkMode && styles.courseChipSelectedDark
                  ]}
                  onPress={() => handleInputChange('course', course)}
                >
                  <Text style={[
                    styles.courseChipText,
                    formData.course === course && styles.courseChipTextSelected,
                    isDarkMode && styles.courseChipTextDark,
                    formData.course === course && isDarkMode && styles.courseChipTextSelectedDark
                  ]}>
                    {course}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            {errors.course && (
              <Text style={styles.errorText}>{errors.course}</Text>
            )}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>Save Changes</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#2a2a2a',
    borderBottomColor: '#444',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerTitleDark: {
    color: '#fff',
  },
  closeButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  labelDark: {
    color: '#fff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#333',
  },
  inputDark: {
    backgroundColor: '#2a2a2a',
    borderColor: '#444',
    color: '#fff',
  },
  inputError: {
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
  },
  courseContainer: {
    flexDirection: 'row',
  },
  courseChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  courseChipDark: {
    backgroundColor: '#2a2a2a',
    borderColor: '#444',
  },
  courseChipSelected: {
    backgroundColor: '#4a90e2',
    borderColor: '#4a90e2',
  },
  courseChipSelectedDark: {
    backgroundColor: '#4a90e2',
    borderColor: '#4a90e2',
  },
  courseChipText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  courseChipTextDark: {
    color: '#fff',
  },
  courseChipTextSelected: {
    color: '#fff',
  },
  courseChipTextSelectedDark: {
    color: '#fff',
  },
  submitButton: {
    backgroundColor: '#28a745',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  submitButtonDisabled: {
    backgroundColor: '#6c757d',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EditProfileModal;
