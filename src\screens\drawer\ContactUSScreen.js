import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking, ScrollView, useWindowDimensions } from 'react-native';
import { Ionicons, MaterialCommunityIcons, FontAwesome } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BottomTabBar from '../../components/BottomTabBar';
import { WebView } from 'react-native-webview';

const EMAIL = '<EMAIL>';
const WHATSAPP = '+916207628282';
const PHONE = '+916207628282';

const ContactUSScreen = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const { width } = useWindowDimensions();

  const handleEmail = () => Linking.openURL(`mailto:${EMAIL}?subject=&body=`);
  const handleWhatsApp = () => Linking.openURL(`https://wa.me/${WHATSAPP.replace('+', '')}`);
  const handleCall = () => Linking.openURL(`tel:${PHONE}`);

  const cardBg = isDarkMode ? '#2a2a2a' : '#fff';
  const textColor = isDarkMode ? '#fff' : '#222';

  return (
    <BottomTabBar>
      <ScrollView contentContainerStyle={[styles.container, { backgroundColor: isDarkMode ? '#181818' : '#f9f9f9' }]}>
        {/* <Text style={[styles.header, { color: textColor }]}>Contact Us</Text> */}

        <TouchableOpacity style={[styles.box, { backgroundColor: cardBg, borderColor: '#0078d4' }]} onPress={handleEmail}>
          <View style={styles.row}>
            <MaterialCommunityIcons name="email-outline" size={28} color="#0078d4" />
            <Text style={[styles.text, { color: '#0078d4' }]}>{EMAIL}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.box, { backgroundColor: cardBg, borderColor: '#25D366' }]} onPress={handleWhatsApp}>
          <View style={styles.row}>
            <FontAwesome name="whatsapp" size={28} color="#25D366" />
            <Text style={[styles.text, { color: '#25D366' }]}>WhatsApp: {WHATSAPP}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.box, { backgroundColor: cardBg, borderColor: '#34b7f1' }]} onPress={handleCall}>
          <View style={styles.row}>
            <Ionicons name="call-outline" size={28} color="#34b7f1" />
            <Text style={[styles.text, { color: '#34b7f1' }]}>{PHONE}</Text>
          </View>
        </TouchableOpacity>

        <View style={[styles.addressBox, { backgroundColor: cardBg, borderColor: '#a259ec' }]}>
          <Ionicons name="location-outline" size={24} color="#a259ec" />
          <Text style={[styles.address, { color: '#a259ec' }]}>
            Pinak Venture, F2/9 Jai Durga Society, Netaji Nagar, Hill No 3, 90ft Road, Sakinaka, Kurla West Mumbai, India
          </Text>
        </View>

        {/* Google Maps Embed */}
        <View style={{ height: 300, marginTop: 16, borderRadius: 12, overflow: 'hidden' }}>
          <WebView
            source={{
              uri: 'https://maps.app.goo.gl/fQfjVzeNBH88xBP7A'
            }}
            style={{ flex: 1 }}
            javaScriptEnabled
            domStorageEnabled
          />
        </View>
      </ScrollView>
    </BottomTabBar>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 24,
    paddingBottom: 60,
  },
  header: {
    fontSize: 26,
    fontWeight: 'bold',
    marginBottom: 24,
    alignSelf: 'center',
  },
  box: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    marginBottom: 18,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#0002',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    marginLeft: 14,
    fontSize: 18,
    fontWeight: '500',
  },
  addressBox: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  address: {
    marginLeft: 14,
    fontSize: 16,
    fontWeight: '400',
    flex: 1,
    flexWrap: 'wrap',
  },
});

export default ContactUSScreen;
