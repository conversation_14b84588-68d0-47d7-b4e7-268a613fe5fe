import React, { useEffect, useRef, useContext } from 'react';
import {
  ScrollView,
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
  LayoutAnimation,
  UIManager,
  Platform
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ThemeContext } from '../context/ThemeContext'; // adjust path if needed
import BottomTabBar from '../components/BottomTabBar';

// Enable layout animation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const faqData = [
  {
    question: "What is Shashtrarth?",
    answer: "Shashtrarth is an online exam portal designed to help students prepare for various competitive exams through mock tests, study materials, and performance analysis."
  },
  {
    question: "Which competitive exams can I prepare for on Shashtrarth?",
    answer: "Shashtrarth provides preparation resources for exams like UPSC, SSC, Banking, Railways, State PSCs, and other government job exams."
  },
  {
    question: "How can I register on Shashtrarth?",
    answer: "You can register on <PERSON>hashtrarth by visiting our website or the in the mobile appy, signing up with your email or mobile number, and setting up your profile."
  },
  {
    question: "Does Shashtrarth provide free mock tests?",
    answer: "Yes, Shashtrarth offers free mock tests for various competitive exams, along with premium test series for in-depth preparation."
  },
  {
    question: "Can I track my progress on Shashtrarth?",
    answer: "Yes, our portal provides detailed performance analysis, including accuracy, speed, and subject-wise strengths and weaknesses."
  },
  {
    question: "How are the mock tests structured on Shashtrarth?",
    answer: "Mock tests on Shashtrarth follow the latest exam pattern, covering all sections with time-bound practice and instant results."
  },
  {
    question: "How can I contact Shashtrarth for support?",
    answer: "You can reach out to our support team through the contact form on our website or email <NAME_EMAIL>."
  },
   {
    question: "Can I use the web and mobile app both in a single account?",
    answer: "Yes, you can seamlessly use the same Shashtrarth account on both the web and mobile app. Your progress, tests, and data stay synced across devices."
  }
];

const FAQScreen = () => {
  const route = useRoute();
  const scrollViewRef = useRef();
  const itemRefs = useRef([]);
  const { isDarkMode } = useContext(ThemeContext);
  const [activeIndex, setActiveIndex] = React.useState(null);

  useEffect(() => {
    const hash = route.params?.hash;
    if (hash && itemRefs.current[hash]) {
      setTimeout(() => {
        itemRefs.current[hash].measureLayout(
          scrollViewRef.current,
          (x, y) => {
            scrollViewRef.current.scrollTo({ y, animated: true });
          }
        );
      }, 300); // delay for layout to complete
    }
  }, [route.params]);

  const toggleExpand = (index) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setActiveIndex(prevIndex => (prevIndex === index ? null : index));
  };

  return (
    <BottomTabBar>
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: isDarkMode ? '#000' : '#fff' },
        ]}
      >
        <Text
          style={[
            styles.header,
            { color: isDarkMode ? '#fff' : '#000' },
          ]}
        >
          Shashtrarth <Text style={[styles.highlight, { color: isDarkMode ? '#90ee90' : 'green' }]}>- FAQs</Text>
        </Text>

        {faqData.map((faq, index) => (
          <View
            key={index}
            ref={(ref) => (itemRefs.current[`#faq${index}`] = ref)}
            style={[
              styles.card,
              {
                backgroundColor: isDarkMode ? '#1a1a1a' : '#f9f9f9',
                borderColor: isDarkMode ? '#444' : '#ddd',
              },
            ]}
          >
            <TouchableOpacity
              onPress={() => toggleExpand(index)}
              style={[
                styles.questionBox,
                { backgroundColor: isDarkMode ? '#333' : '#e6ffe6' },
              ]}
            >
              <Text
                style={[
                  styles.question,
                  { color: isDarkMode ? '#fff' : '#000' },
                ]}
              >
                {faq.question}
              </Text>
            </TouchableOpacity>

            {activeIndex === index && (
              <View
                style={[
                  styles.answerBox,
                  { backgroundColor: isDarkMode ? '#000' : '#fff' },
                ]}
              >
                <Text style={[styles.answer, { color: isDarkMode ? '#ccc' : '#333' }]}>
                  {faq.answer}
                </Text>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </BottomTabBar>
  );
};

export default FAQScreen;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    paddingBottom: 40,
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  highlight: {
    fontWeight: 'bold',
  },
  card: {
    marginBottom: 12,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 2,
    borderWidth: 1,
  },
  questionBox: {
    padding: 16,
  },
  question: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  answerBox: {
    padding: 16,
  },
  answer: {
    fontSize: 14,
  }
});
