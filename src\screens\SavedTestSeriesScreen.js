import React, { useContext } from 'react';
import { View, Text, StyleSheet, FlatList, SafeAreaView, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { toggleSaveTestSeries } from '../redux/dashboardSlice';
import Toast from 'react-native-toast-message';

export default function SavedTestSeriesScreen({ navigation }) {
  const { savedTestSeries } = useSelector((state) => state.dashboard);
  const { isDarkMode } = useContext(ThemeContext);
  const dispatch = useDispatch();

  const handleUnsave = (test) => {
    dispatch(toggleSaveTestSeries(test));
    Toast.show({
      type: 'info',
      text1: 'Test Series Removed',
      text2: 'Removed from your saved items',
      visibilityTime: 2000,
      position: 'bottom',
    });
  };

  if (!savedTestSeries || savedTestSeries.length === 0) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.emptyContainer, isDarkMode && styles.emptyContainerDark]}>
          <Icon name="bookmark-outline" size={48} color={isDarkMode ? '#666' : '#999'} />
          <Text style={[styles.emptyText, isDarkMode && styles.emptyTextDark]}>
            No saved test series yet
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderTestSeriesItem = ({ item }) => (
    <View style={[styles.card, isDarkMode && styles.cardDark]}>
      <View style={styles.cardHeader}>
        <View style={styles.badgeContainer}>
          <View style={[styles.badge, { backgroundColor: '#198754' }]}>
            <Icon name="notebook" size={16} color="#fff" style={{ marginRight: 5 }} />
            <Text style={styles.badgeText}>{item.course}</Text>
          </View>
          <View style={[styles.badge, { backgroundColor: item.paid ? '#dc3545' : '#198754' }]}>
            <Text style={styles.badgeText}>{item.paid ? 'Paid' : 'Free'}</Text>
          </View>
        </View>
      </View>

      <View style={styles.cardContent}>
        <Text style={[styles.title, isDarkMode && styles.textDark]}>
          {item.sub_course_name}
        </Text>
        <View style={styles.testInfo}>
          <Text style={[styles.smallText, isDarkMode && styles.textDark]}>
            {item.paper_details?.length > 0 ? `${item.paper_details.length} Tests` : '0 Tests'}
          </Text>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.removeButton]}
            onPress={() => handleUnsave(item)}
          >
            <Icon name="bookmark-remove" size={16} color="#fff" />
            <Text style={styles.buttonText}>Remove</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.viewButton]}            onPress={() => {
              const parent = navigation.getParent();
              const route = parent.getCurrentRoute();
              const params = { testSeries: item };
              
              if (route.name === 'MainTabs') {
                navigation.navigate('TestSeriesDetails', params);
              } else {
                parent.navigate('MainTabs', {
                  screen: 'SavedTab',
                  params: {
                    screen: 'TestSeriesDetails',
                    params
                  }
                });
              }
            }}
            >
            <Icon name="eye" size={16} color="#198754" />
            <Text style={[styles.buttonText, { color: '#198754' }]}>View</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={[styles.cardFooter, isDarkMode && styles.cardFooterDark]}>
        <View style={styles.languageContainer}>
          <Icon name="translate" size={16} color={isDarkMode ? '#999' : '#666'} />
          <Text style={[styles.languageText, isDarkMode && styles.textDark]}>
            {item.language}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <FlatList
        data={savedTestSeries}
        renderItem={renderTestSeriesItem}
        keyExtractor={item => item.subcourse_slug}
        contentContainerStyle={[styles.listContent, isDarkMode && styles.listContentDark]}
        style={[styles.flatList, isDarkMode && styles.flatListDark]}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  emptyContainerDark: {
    backgroundColor: '#121212',
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginTop: 16,
  },
  emptyTextDark: {
    color: '#e0e0e0',
  },
  listContent: {
    padding: 16,
    paddingBottom: 20,
    backgroundColor: '#ffffff',
  },
  listContentDark: {
    backgroundColor: '#121212',
  },
  flatList: {
    backgroundColor: '#ffffff',
  },
  flatListDark: {
    backgroundColor: '#121212',
  },
  card: {
    marginBottom: 15,
    borderRadius: 8,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardDark: {
    backgroundColor: '#2d2d2d',
  },
  cardHeader: {
    padding: 10,
  },
  cardContent: {
    padding: 10,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#000000',
  },
  textDark: {
    color: '#e0e0e0',
  },
  testInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  smallText: {
    fontSize: 12,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  removeButton: {
    flex: 3,
    backgroundColor: '#dc3545',
  },
  viewButton: {
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#198754',
  },
  buttonText: {
    fontSize: 12,
    color: '#fff',
    marginLeft: 4,
    fontWeight: '500',
  },
  cardFooter: {
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  cardFooterDark: {
    backgroundColor: '#1e1e1e',
  },
  languageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  languageText: {
    fontSize: 12,
    color: '#666',
  }
});
