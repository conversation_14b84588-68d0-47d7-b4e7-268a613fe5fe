import React, { useEffect, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { ThemeContext } from '../context/ThemeContext';
import {
  logEvent,
  setUserId,
  setUserProperties,
  logScreenView,
  logLogin,
  logSignUp,
  logPurchase,
  logSearch,
  logViewContent,
} from '../utils/analytics';

const AnalyticsExampleScreen = ({ navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);

  useEffect(() => {
    // Log screen view when component mounts
    logScreenView('AnalyticsExample', 'AnalyticsExampleScreen');
  }, []);

  const handleSetUserId = () => {
    const userId = 'user_123456';
    setUserId(userId);
    Alert.alert('Analytics', `User ID set to: ${userId}`);
  };

  const handleSetUserProperties = () => {
    const properties = {
      user_type: 'premium',
      subscription_status: 'active',
      preferred_language: 'english',
      registration_date: '2024-01-15',
    };
    setUserProperties(properties);
    Alert.alert('Analytics', 'User properties set successfully');
  };

  const handleLogLogin = () => {
    logLogin('email');
    Alert.alert('Analytics', 'Login event logged');
  };

  const handleLogSignUp = () => {
    logSignUp('email');
    Alert.alert('Analytics', 'Sign up event logged');
  };

  const handleLogPurchase = () => {
    const purchaseData = {
      transactionId: 'txn_' + Date.now(),
      value: 299.99,
      currency: 'INR',
      items: [
        {
          item_id: 'package_001',
          item_name: 'Premium Package',
          category: 'subscription',
          quantity: 1,
          price: 299.99,
        },
      ],
    };
    logPurchase(purchaseData);
    Alert.alert('Analytics', 'Purchase event logged');
  };

  const handleLogSearch = () => {
    logSearch('SBI PO Mock Test');
    Alert.alert('Analytics', 'Search event logged');
  };

  const handleLogViewContent = () => {
    logViewContent('package', 'pkg_001', 'All India Shash Essay Competition');
    Alert.alert('Analytics', 'View content event logged');
  };

  const handleCustomEvent = () => {
    logEvent('custom_button_clicked', {
      button_name: 'Custom Analytics Button',
      screen: 'AnalyticsExample',
      user_action: 'test_custom_event',
      timestamp: new Date().toISOString(),
    });
    Alert.alert('Analytics', 'Custom event logged');
  };

  const buttonStyle = [
    styles.button,
    isDarkMode && styles.buttonDark,
  ];

  const textStyle = [
    styles.buttonText,
    isDarkMode && styles.buttonTextDark,
  ];

  return (
    <ScrollView
      style={[styles.container, isDarkMode && styles.containerDark]}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={[styles.title, isDarkMode && styles.titleDark]}>
        Firebase Analytics Examples
      </Text>

      <Text style={[styles.description, isDarkMode && styles.descriptionDark]}>
        This screen demonstrates various Firebase Analytics events. 
        Check your Firebase console to see the events being logged.
      </Text>

      <TouchableOpacity style={buttonStyle} onPress={handleSetUserId}>
        <Text style={textStyle}>Set User ID</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleSetUserProperties}>
        <Text style={textStyle}>Set User Properties</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleLogLogin}>
        <Text style={textStyle}>Log Login Event</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleLogSignUp}>
        <Text style={textStyle}>Log Sign Up Event</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleLogPurchase}>
        <Text style={textStyle}>Log Purchase Event</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleLogSearch}>
        <Text style={textStyle}>Log Search Event</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleLogViewContent}>
        <Text style={textStyle}>Log View Content Event</Text>
      </TouchableOpacity>

      <TouchableOpacity style={buttonStyle} onPress={handleCustomEvent}>
        <Text style={textStyle}>Log Custom Event</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[buttonStyle, styles.backButton]}
        onPress={() => navigation.goBack()}
      >
        <Text style={textStyle}>Go Back</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  titleDark: {
    color: '#fff',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
    lineHeight: 22,
  },
  descriptionDark: {
    color: '#ccc',
  },
  button: {
    backgroundColor: '#00c853',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    alignItems: 'center',
  },
  buttonDark: {
    backgroundColor: '#4CAF50',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonTextDark: {
    color: '#fff',
  },
  backButton: {
    backgroundColor: '#666',
    marginTop: 20,
  },
});

export default AnalyticsExampleScreen;
